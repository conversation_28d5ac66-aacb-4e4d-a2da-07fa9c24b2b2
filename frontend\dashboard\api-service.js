/**
 * Dashboard API Service - Unified Implementation
 *
 * Direct replacement of the original dashboard API service
 * using the unified service with dashboard context.
 */

// Use the globally available UnifiedApiService class
document.addEventListener('DOMContentLoaded', function() {
  try {
    // Check if UnifiedApiService is available globally
    if (window.UnifiedApiService) {
      console.log('Creating Dashboard API Service with global UnifiedApiService');
      
      // Create dashboard-specific instance
      const apiService = new window.UnifiedApiService();
      apiService.moduleContext = 'dashboard';
      apiService.initializeConfiguration();
      
      // Add test suite selection methods
      apiService.getTestSuites = async (filters = {}) => {
        console.log('Fetching test suites with filters:', filters);
        
        try {
          // Use the new filtered endpoint for better performance and consistency
          const params = {};
          
          // Add filters if they are provided and not empty
          if (filters.level && filters.level !== '' && filters.level !== 'all') {
            params.level = filters.level;
          }
          if (filters.version && filters.version !== '' && filters.version !== 'all') {
            params.version = filters.version;
          }
          
          // Use the filtered endpoint if filters are provided
          let endpoint = '/api/test-suites';
          if (params.level || params.version) {
            endpoint = '/api/test-suites/filtered';
          }
          
          // Make the API request using the new endpoint
          const response = await apiService.getRequest(endpoint, params);
          
          if (response.success) {
            return response.data || [];
          } else {
            throw new Error(response.message || 'Failed to fetch test suites');
          }
        } catch (error) {
          console.error('Error fetching test suites:', error);
          throw error;
        }
      };
      
      // Add method to get filtered test suites (multi-select support)
      apiService.getFilteredTestSuites = async (level = '', version = '') => {
        console.log('Fetching filtered test suites:', { level, version });
        
        try {
          const params = {};
          if (level && level !== '') params.level = level;
          if (version && version !== '') params.version = version;
          
          const response = await apiService.getRequest('/api/test-suites/filtered', params);
          
          if (response.success) {
            return response.data || [];
          } else {
            throw new Error(response.message || 'Failed to fetch filtered test suites');
          }
        } catch (error) {
          console.error('Error fetching filtered test suites:', error);
          throw error;
        }
      };
      
      // Add method to get test suite by ID
      apiService.getTestSuiteById = async (ts_id) => {
        console.log('Fetching test suite by ID:', ts_id);
        
        try {
          const response = await apiService.getRequest(`/api/test-suites/${ts_id}`);
          
          if (response.success) {
            return response.data;
          } else {
            throw new Error(response.message || 'Test suite not found');
          }
        } catch (error) {
          console.error('Error fetching test suite by ID:', error);
          throw error;
        }
      };
      
      // Add method to get filter options
      apiService.getFilterOptions = async () => {
        console.log('Fetching filter options');
        
        try {
          const response = await apiService.getRequest('/api/test-suites/filter-options');
          
          if (response.success) {
            return response.data || { levels: [], versions: [] };
          } else {
            throw new Error(response.message || 'Failed to fetch filter options');
          }
        } catch (error) {
          console.error('Error fetching filter options:', error);
          throw error;
        }
      };
      
      apiService.runTestSuite = async (suiteId) => {
        if (!suiteId) {
          throw new Error('Test suite ID is required');
        }
        
        console.log(`Running test suite with ID: ${suiteId}`);
        
        try {
          // Make the API request using the predefined endpoint
          const data = await apiService.postRequest('/api/suite-runner', { ts_id: suiteId });
          return {
            success: true,
            testId: data.testId,
            message: data.message
          };
        } catch (error) {
          console.error('Error running test suite:', error);
          throw error;
        }
      };
      
      // Add the missing login method that the frontend expects
      apiService.login = async (username, password) => {
        if (!username || !password) {
          throw new Error('Username and password are required');
        }
        
        console.log(`Attempting login for user: ${username}`);
        
        try {
          // Set credentials using the underlying UnifiedApiService method
          apiService.setCredentials(username, password);
          
          // For dashboard module, we consider login successful if credentials are set
          // The actual API authentication happens with each request
          console.log('Login successful, credentials set');
          
          // Dispatch the apiservice-ready event that other parts of the app expect
          setTimeout(() => {
            document.dispatchEvent(new CustomEvent('apiservice-ready', {
              detail: { apiService: apiService, username: username }
            }));
            console.log('Dispatched apiservice-ready event after login');
          }, 50);
          
          return {
            success: true,
            username: username,
            message: 'Login successful'
          };
        } catch (error) {
          console.error('Login failed:', error);
          throw new Error(`Login failed: ${error.message}`);
        }
      };
      
      // Make it globally available (preserving existing interface)
      window.apiService = apiService;
      
      console.log('Dashboard API Service (Unified) initialized successfully');
    } else {
      console.error('UnifiedApiService not found! Make sure unified-api-service.js is loaded before this file.');
    }
  } catch (error) {
    console.error('Error initializing Dashboard API Service:', error);
  }
});

// Legacy export support
if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {
  module.exports = window.apiService || {};
}
