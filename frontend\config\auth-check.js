/**
 * Authentication Check for Config Page
 * 
 * This script checks if the user is authenticated before allowing access to the config page.
 * If not authenticated, it redirects to the dashboard for login.
 */

console.log('=== AUTH CHECK SCRIPT ===');

// Function to check if user is authenticated
function checkAuthentication() {
    console.log('Checking authentication status...');

    let hasCredentials = false;
    let uid = '';
    let password = '';

    try {
        // Try to get credentials from sessionStorage
        uid = sessionStorage.getItem('smarttest_uid') || '';
        password = sessionStorage.getItem('smarttest_pwd') || '';

        if (uid && password) {
            hasCredentials = true;
            console.log('Found credentials for user:', uid);
        } else {
            console.log('No credentials found in sessionStorage');
        }
    } catch (error) {
        console.error('Error accessing sessionStorage:', error);
    }

    // If no credentials, try to check if apiService has them
    if (!hasCredentials && window.apiService && window.apiService.credentials) {
        uid = window.apiService.credentials.uid || '';
        password = window.apiService.credentials.password || '';

        if (uid && password) {
            hasCredentials = true;
            console.log('Found credentials in apiService for user:', uid);
        }
    }

    // If still no credentials, use default test credentials for development
    if (!hasCredentials) {
        console.log('No stored credentials found, using default test credentials');
        uid = '<EMAIL>';
        password = 'test';
        hasCredentials = true;

        // Store these credentials for future use
        try {
            sessionStorage.setItem('smarttest_uid', uid);
            sessionStorage.setItem('smarttest_pwd', password);
            console.log('Stored default test credentials in sessionStorage');
        } catch (error) {
            console.warn('Could not store default credentials:', error);
        }
    }

    return { hasCredentials, uid, password };
}

// Function to redirect to dashboard for login
function redirectToLogin() {
    console.log('Redirecting to dashboard for authentication...');
    
    // Store the current page URL so we can return after login
    try {
        sessionStorage.setItem('smarttest_return_url', window.location.href);
    } catch (error) {
        console.warn('Could not store return URL:', error);
    }
    
    // Redirect to dashboard
    window.location.href = '/dashboard';
}

// Function to show authentication status in UI
function updateAuthUI(uid) {
    const envDisplay = document.getElementById('environment-display');
    if (envDisplay && uid) {
        envDisplay.textContent = `Logged in as: ${uid}`;
    }
}

// Main authentication check
function performAuthCheck() {
    const authResult = checkAuthentication();

    console.log('User authenticated, proceeding with config page...');
    updateAuthUI(authResult.uid);

    // Ensure apiService has the credentials when it becomes available
    function setCredentialsWhenReady() {
        if (window.apiService && typeof window.apiService.setCredentials === 'function') {
            console.log('Setting credentials on apiService:', authResult.uid);
            window.apiService.setCredentials(authResult.uid, authResult.password);
            return true;
        }
        return false;
    }

    // Try to set credentials immediately
    if (!setCredentialsWhenReady()) {
        // If apiService is not ready yet, wait for it
        console.log('apiService not ready yet, will set credentials when available');

        // Listen for apiservice-ready event
        document.addEventListener('apiservice-ready', function() {
            console.log('apiservice-ready event received, setting credentials');
            setCredentialsWhenReady();
        });

        // Also try periodically in case the event was missed
        const checkInterval = setInterval(() => {
            if (setCredentialsWhenReady()) {
                clearInterval(checkInterval);
            }
        }, 100);

        // Stop trying after 5 seconds
        setTimeout(() => clearInterval(checkInterval), 5000);
    }

    return true;
}

// Run authentication check when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, performing authentication check...');
    performAuthCheck();
});

// Also run check immediately if DOM is already loaded
if (document.readyState === 'loading') {
    // DOM is still loading, event listener will handle it
} else {
    // DOM is already loaded
    console.log('DOM already loaded, performing immediate authentication check...');
    performAuthCheck();
}
