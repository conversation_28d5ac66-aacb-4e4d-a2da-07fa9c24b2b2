/**
 * Polling Coordinator - Manages coordinated polling for all config module components
 * Implements different polling strategies for different data types
 */

class PollingCoordinator {
    constructor() {
        this.intervals = new Map();
        this.subscribers = new Map();
        this.isPolling = false;
        
        // Optimized polling intervals based on user requirements
        this.config = {
            activeTests: {
                interval: 2000, // 2 seconds - for tests running 15 sec to 5-6 min
                endpoint: 'activeTests',
                subscribers: []
            },
            recentRuns: {
                interval: 8000, // 8 seconds - for completed test history
                endpoint: 'recentRuns',
                subscribers: []
            },
            testDetails: {
                interval: 0, // On-demand only
                endpoint: 'testDetails',
                subscribers: []
            }
        };
        
        // Data store for sharing between components
        this.dataStore = {
            activeTests: [],
            recentRuns: [],
            lastUpdate: {
                activeTests: 0,
                recentRuns: 0
            }
        };
        
        console.log('PollingCoordinator initialized with optimized intervals');
    }

    /**
     * Subscribe a component to data updates
     * @param {string} dataType - Type of data (activeTests, recentRuns)
     * @param {Function} callback - Callback function to receive updates
     * @param {string} componentId - Unique identifier for the component
     */
    subscribe(dataType, callback, componentId) {
        if (!this.config[dataType]) {
            console.error(`Invalid data type: ${dataType}`);
            return;
        }

        const subscription = { callback, componentId, dataType };
        this.config[dataType].subscribers.push(subscription);
        
        console.log(`Component ${componentId} subscribed to ${dataType}`);
        
        // Send current data immediately if available
        if (this.dataStore[dataType].length > 0) {
            callback(this.dataStore[dataType]);
        }
        
        // Start polling if this is the first subscriber
        if (!this.isPolling) {
            this.startPolling();
        }
    }

    /**
     * Unsubscribe a component from data updates
     * @param {string} componentId - Component identifier
     */
    unsubscribe(componentId) {
        for (const [dataType, config] of Object.entries(this.config)) {
            config.subscribers = config.subscribers.filter(sub => sub.componentId !== componentId);
        }
        console.log(`Component ${componentId} unsubscribed from all data types`);
    }

    /**
     * Start coordinated polling
     */
    startPolling() {
        if (this.isPolling) {
            console.log('Polling already active');
            return;
        }

        this.isPolling = true;
        console.log('Starting coordinated polling...');

        // Start polling for active tests (fast interval)
        this.startDataTypePolling('activeTests');
        
        // Start polling for recent runs (slower interval)
        this.startDataTypePolling('recentRuns');
    }

    startDataTypePolling(dataType) {
        const config = this.config[dataType];
        
        if (config.subscribers.length === 0) {
            console.log(`No subscribers for ${dataType}, skipping polling`);
            return;
        }

        // Poll immediately
        this.pollDataType(dataType);
        
        // Set up interval
        const intervalId = setInterval(() => {
            this.pollDataType(dataType);
        }, config.interval);
        
        this.intervals.set(dataType, intervalId);
        console.log(`Started polling for ${dataType} every ${config.interval}ms`);
    }

    async pollDataType(dataType) {
        const config = this.config[dataType];
        
        if (config.subscribers.length === 0) {
            this.stopDataTypePolling(dataType);
            return;
        }

        try {
            console.log(`Polling ${dataType}...`);
            
            let data;
            const requestKey = `${dataType}_${Date.now()}`;
            
            if (dataType === 'activeTests') {
                data = await this.pollActiveTests(requestKey);
            } else if (dataType === 'recentRuns') {
                data = await this.pollRecentRuns(requestKey);
            }

            if (data) {
                this.updateDataStore(dataType, data);
                this.notifySubscribers(dataType, data);
            }
            
        } catch (error) {
            console.error(`Error polling ${dataType}:`, error);
            this.notifySubscribersError(dataType, error);
        }
    }

    async pollActiveTests(requestKey) {
        // Use request manager for deduplication and caching
        return await window.requestManager.executeRequest(
            requestKey,
            async () => {
                // Get recent runs and filter for active tests
                const recentRuns = await window.apiService.getRecentRuns({ 
                    limit: 100, // Get enough to find all active tests
                    status: 'running,queued' // Filter for active statuses only
                });
                
                // Filter for truly active tests (no end_time and running/queued status)
                const activeTests = recentRuns.filter(run => 
                    !run.end_time && 
                    (run.status === 'running' || run.status === 'queued') &&
                    run.tc_id // Ensure it's a single test case, not a suite
                );
                
                console.log(`Found ${activeTests.length} active tests`);
                return activeTests;
            },
            'activeTests'
        );
    }

    async pollRecentRuns(requestKey) {
        // Use request manager for deduplication and caching
        return await window.requestManager.executeRequest(
            requestKey,
            async () => {
                // Get only single test case runs (not suites), limited to 50
                const recentRuns = await window.apiService.getRecentRuns({ 
                    limit: 50,
                    type: 'single_case' // Filter for single test cases only
                });
                
                console.log(`Retrieved ${recentRuns.length} recent single test case runs`);
                return recentRuns;
            },
            'recentRuns'
        );
    }

    updateDataStore(dataType, data) {
        this.dataStore[dataType] = data;
        this.dataStore.lastUpdate[dataType] = Date.now();
    }

    notifySubscribers(dataType, data) {
        const config = this.config[dataType];
        config.subscribers.forEach(subscription => {
            try {
                subscription.callback(data);
            } catch (error) {
                console.error(`Error notifying subscriber ${subscription.componentId}:`, error);
            }
        });
    }

    notifySubscribersError(dataType, error) {
        const config = this.config[dataType];
        config.subscribers.forEach(subscription => {
            if (subscription.errorCallback) {
                try {
                    subscription.errorCallback(error);
                } catch (callbackError) {
                    console.error(`Error in error callback for ${subscription.componentId}:`, callbackError);
                }
            }
        });
    }

    stopDataTypePolling(dataType) {
        const intervalId = this.intervals.get(dataType);
        if (intervalId) {
            clearInterval(intervalId);
            this.intervals.delete(dataType);
            console.log(`Stopped polling for ${dataType}`);
        }
    }

    /**
     * Stop all polling
     */
    stopPolling() {
        for (const dataType of this.intervals.keys()) {
            this.stopDataTypePolling(dataType);
        }
        this.isPolling = false;
        console.log('All polling stopped');
    }

    /**
     * Get current data for a specific type
     * @param {string} dataType - Type of data to retrieve
     * @returns {Array} Current data
     */
    getCurrentData(dataType) {
        return this.dataStore[dataType] || [];
    }

    /**
     * Force refresh of specific data type
     * @param {string} dataType - Type of data to refresh
     */
    async forceRefresh(dataType) {
        console.log(`Force refreshing ${dataType}...`);
        await this.pollDataType(dataType);
    }

    /**
     * Get polling status and metrics
     */
    getStatus() {
        return {
            isPolling: this.isPolling,
            activeIntervals: Array.from(this.intervals.keys()),
            subscriberCounts: Object.fromEntries(
                Object.entries(this.config).map(([key, config]) => [key, config.subscribers.length])
            ),
            lastUpdates: this.dataStore.lastUpdate,
            dataStoreSizes: Object.fromEntries(
                Object.entries(this.dataStore).filter(([key]) => Array.isArray(this.dataStore[key]))
                    .map(([key, data]) => [key, data.length])
            )
        };
    }
}

// Create global instance
window.pollingCoordinator = new PollingCoordinator();

export default PollingCoordinator;
