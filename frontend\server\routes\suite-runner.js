/**
 * Suite Runner Routes
 */
const express = require('express');
const router = express.Router();
const { validateCredentials } = require('../middleware/auth');
const { runTest } = require('../services/case-runner');

// SuiteRunner API proxy endpoint
router.post('/suite-runner', validateCredentials, async (req, res) => {
  try {
    const requestBody = { ...req.body };
    console.log(`[API /suite-runner] Received request with params:`, requestBody);

    if (!requestBody.ts_id) {
      return res.status(400).json({
        success: false,
        message: 'ts_id (test suite ID) is required.'
      });
    }

    try {
      // Run the test using the case-runner service
      const result = await runTest(requestBody);

      return res.json({
        success: true,
        ...result
      });
    } catch (error) {
      console.error(`[API /suite-runner] Error running test suite:`, error);
      return res.status(500).json({
        success: false,
        message: `Failed to run test suite: ${error.message}`
      });
    }
  } catch (error) {
    console.error('Error in suite-runner endpoint:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

module.exports = router;
