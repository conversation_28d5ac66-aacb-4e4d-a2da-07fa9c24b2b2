/**
 * dashboard-auth.js
 *
 * This module handles all user authentication logic, including login/logout UI,
 * form submissions, and session state management.
 */

import { config } from './dashboard-config.js';
import * as api from './dashboard-api.js';
import { notifications } from './dashboard-ui.js';

/**
 * Initializes all authentication-related event listeners.
 */
export function initializeAuth() {
    config.elements.loginForm?.addEventListener('submit', handleLoginSubmit);
    config.elements.loginButton?.addEventListener('click', showLoginModal);
    config.elements.logoutButton?.addEventListener('click', handleLogout);

    // Check initial login state
    updateLoginUI();
}

/**
 * Shows the login modal dialog.
 */
function showLoginModal() {
    if (config.elements.loginModal) {
        config.elements.loginModal.style.display = 'block';
        config.elements.loginModal.classList.add('active');
    }
    config.elements.usernameInput?.focus();
}

/**
 * Hides the login modal dialog.
 */
function hideLoginModal() {
    if (config.elements.loginModal) {
        config.elements.loginModal.style.display = 'none';
        config.elements.loginModal.classList.remove('active');
    }
}

/**
 * Handles the submission of the login form.
 * @param {Event} event - The form submission event.
 */
async function handleLoginSubmit(event) {
    event.preventDefault();
    const username = config.elements.usernameInput?.value;
    const password = config.elements.passwordInput?.value;

    if (!username || !password) {
        notifications.error('Username and password are required.', 'Login Error');
        return;
    }

    notifications.info('Logging in...', 'Please wait');
    try {
        const success = await api.login(username, password);
        if (success) {
            handleSuccessfulLogin(username);
        } else {
            notifications.error('Invalid credentials. Please try again.', 'Login Failed');
            if (config.elements.loginStatus) {
                config.elements.loginStatus.textContent = 'Invalid credentials.';
            }
        }
    } catch (error) {
        console.error('Login error:', error);
        notifications.error('An unexpected error occurred during login.', 'Login Error');
    }
}

/**
 * Handles the UI updates and state changes for a successful login.
 * @param {string} username - The username of the logged-in user.
 */
function handleSuccessfulLogin(username) {
    hideLoginModal();
    updateLoginUI(true, username);
    notifications.success(`Welcome, ${username}!`, 'Login Successful');
    // The main app will re-initialize or refresh data as needed.
    // We can dispatch an event to notify other modules.
    document.dispatchEvent(new CustomEvent('auth-state-changed', { detail: { loggedIn: true } }));
}

/**
 * Handles the user logout process.
 */
async function handleLogout() {
    try {
        await api.logout();
        updateLoginUI(false);
        notifications.info('You have been logged out.', 'Logout');
        document.dispatchEvent(new CustomEvent('auth-state-changed', { detail: { loggedIn: false } }));
    } catch (error) {
        console.error('Logout error:', error);
        notifications.error('An error occurred during logout.', 'Logout Error');
    }
}

/**
 * Updates the header and other UI elements based on login state.
 * @param {boolean} isLoggedIn - Whether the user is logged in.
 * @param {string} [username] - The username if logged in.
 */
function updateLoginUI(isLoggedIn, username = '') {
    const { loginButton, logoutButton, userDisplay } = config.elements;

    if (isLoggedIn) {
        if (loginButton) loginButton.style.display = 'none';
        if (logoutButton) logoutButton.style.display = 'inline-block';
        if (userDisplay) userDisplay.textContent = `Logged in as: ${username}`;
    } else {
        if (loginButton) loginButton.style.display = 'inline-block';
        if (logoutButton) logoutButton.style.display = 'none';
        if (userDisplay) userDisplay.textContent = 'Not logged in';
    }
}
