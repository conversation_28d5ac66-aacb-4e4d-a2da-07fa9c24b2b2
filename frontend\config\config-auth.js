/**
 * config-auth.js
 *
 * Authentication handler for the Test Runner page that matches Dashboard functionality
 */

class ConfigAuth {
    constructor() {
        this.elements = {
            loginModal: document.getElementById('login-modal'),
            loginForm: document.getElementById('login-form'),
            loginButton: document.getElementById('login-button'),
            logoutButton: document.getElementById('logout-button'),
            usernameInput: document.getElementById('username'),
            passwordInput: document.getElementById('password'),
            loginStatus: document.getElementById('login-status'),
            userDisplay: document.getElementById('user-display'),
            environmentDisplay: document.getElementById('environment-display')
        };

        this.isAuthenticated = false;
        this.currentUser = null;
    }

    /**
     * Initialize authentication system
     */
    init() {
        this.bindEvents();
        this.checkInitialAuth();
        this.updateUI();
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        if (this.elements.loginForm) {
            this.elements.loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        if (this.elements.loginButton) {
            this.elements.loginButton.addEventListener('click', () => this.showLoginModal());
        }

        if (this.elements.logoutButton) {
            this.elements.logoutButton.addEventListener('click', () => this.handleLogout());
        }

        // Close modal when clicking outside
        if (this.elements.loginModal) {
            this.elements.loginModal.addEventListener('click', (e) => {
                if (e.target === this.elements.loginModal) {
                    this.hideLoginModal();
                }
            });
        }
    }

    /**
     * Check for existing authentication
     */
    checkInitialAuth() {
        try {
            const uid = sessionStorage.getItem('smarttest_uid');
            const password = sessionStorage.getItem('smarttest_pwd');

            if (uid && password) {
                this.isAuthenticated = true;
                this.currentUser = uid;
                this.hideLoginModal();

                // Update API service with credentials
                if (window.apiService) {
                    window.apiService.setCredentials(uid, password);
                }

                console.log('Found existing authentication for:', uid);
            } else {
                this.showLoginModal();
            }
        } catch (error) {
            console.error('Error checking authentication:', error);
            this.showLoginModal();
        }
    }

    /**
     * Show login modal
     */
    showLoginModal() {
        if (this.elements.loginModal) {
            this.elements.loginModal.style.display = 'flex';
            this.elements.loginModal.classList.add('active');
        }
        if (this.elements.usernameInput) {
            this.elements.usernameInput.focus();
        }
    }

    /**
     * Hide login modal
     */
    hideLoginModal() {
        if (this.elements.loginModal) {
            this.elements.loginModal.style.display = 'none';
            this.elements.loginModal.classList.remove('active');
        }
    }

    /**
     * Handle login form submission
     */
    async handleLogin(event) {
        event.preventDefault();

        const username = this.elements.usernameInput?.value.trim();
        const password = this.elements.passwordInput?.value.trim();

        if (!username || !password) {
            this.showLoginError('Please enter both username and password');
            return;
        }

        try {
            // Store credentials
            sessionStorage.setItem('smarttest_uid', username);
            sessionStorage.setItem('smarttest_pwd', password);

            // Update authentication state
            this.isAuthenticated = true;
            this.currentUser = username;

            // Hide modal and update UI
            this.hideLoginModal();
            this.updateUI();
            this.hideLoginError();

            console.log('Login successful for:', username);

            // Update API service with credentials
            if (window.apiService) {
                window.apiService.setCredentials(username, password);
            }

            // Trigger a custom event for other components
            window.dispatchEvent(new CustomEvent('auth:login', {
                detail: { username }
            }));

        } catch (error) {
            console.error('Login error:', error);
            this.showLoginError('Login failed. Please try again.');
        }
    }

    /**
     * Handle logout
     */
    handleLogout() {
        try {
            // Clear stored credentials
            sessionStorage.removeItem('smarttest_uid');
            sessionStorage.removeItem('smarttest_pwd');

            // Update authentication state
            this.isAuthenticated = false;
            this.currentUser = null;

            // Clear API service credentials
            if (window.apiService && window.apiService.clearCredentials) {
                window.apiService.clearCredentials();
            }

            // Update UI and show login modal
            this.updateUI();
            this.showLoginModal();

            console.log('Logout successful');

            // Trigger a custom event for other components
            window.dispatchEvent(new CustomEvent('auth:logout'));

        } catch (error) {
            console.error('Logout error:', error);
        }
    }

    /**
     * Update UI based on authentication state
     */
    updateUI() {
        if (this.elements.userDisplay) {
            this.elements.userDisplay.textContent = this.isAuthenticated 
                ? `Logged in as: ${this.currentUser}` 
                : 'Not logged in';
        }

        if (this.elements.loginButton) {
            this.elements.loginButton.style.display = this.isAuthenticated ? 'none' : 'inline-flex';
        }

        if (this.elements.logoutButton) {
            this.elements.logoutButton.style.display = this.isAuthenticated ? 'inline-flex' : 'none';
        }

        // Update environment display
        if (this.elements.environmentDisplay) {
            this.elements.environmentDisplay.textContent = 'Environment: Development';
        }
    }

    /**
     * Show login error message
     */
    showLoginError(message) {
        if (this.elements.loginStatus) {
            this.elements.loginStatus.textContent = message;
            this.elements.loginStatus.style.display = 'block';
        }
    }

    /**
     * Hide login error message
     */
    hideLoginError() {
        if (this.elements.loginStatus) {
            this.elements.loginStatus.style.display = 'none';
        }
    }

    /**
     * Get current authentication status
     */
    getAuthStatus() {
        return {
            isAuthenticated: this.isAuthenticated,
            user: this.currentUser,
            credentials: this.isAuthenticated ? {
                uid: sessionStorage.getItem('smarttest_uid'),
                password: sessionStorage.getItem('smarttest_pwd')
            } : null
        };
    }
}

// Initialize authentication when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.configAuth = new ConfigAuth();
    window.configAuth.init();
});

// Export for use by other modules
window.ConfigAuth = ConfigAuth;
