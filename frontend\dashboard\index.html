<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Automation Dashboard</title>
    <!-- Replace Bootstrap with Fluent UI -->
    <link href="https://static2.sharepointonline.com/files/fabric/office-ui-fabric-core/11.0.0/css/fabric.min.css" rel="stylesheet">
    <link href="https://res-1.cdn.office.net/files/fabric-cdn-prod_20230815.002/office-ui-fabric-core/11.0.0/css/fabric.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="scrollbar-fix.css">
    <link rel="stylesheet" href="test-card-improvements.css">
    <!-- Load shared services in the correct order -->
    <script type="module" src="../shared/services/base-api-service.js"></script>
    <script src="../shared/services/unified-api-service.js"></script>
    <script src="../shared/services/external-api-service.js"></script>
    <!-- Load module-specific API service wrapper -->
    <script src="api-service.js"></script>
</head>
<body class="ms-Fabric">
    <!-- Login Modal for API Credentials -->
    <div id="login-modal" class="ms-modal active">
        <div class="ms-modal-content">
            <h2 class="ms-font-xl">Login to Test System</h2>
            <form id="login-form">
                <div class="ms-form-group">
                    <label class="ms-Label">Username</label>
                    <input class="ms-TextField-field" type="text" id="username" placeholder="Enter your username" required>
                </div>
                <div class="ms-form-group">
                    <label class="ms-Label">Password</label>
                    <input class="ms-TextField-field" type="password" id="password" placeholder="Enter your password" required>
                </div>
                <div class="ms-form-actions">
                    <button type="submit" class="ms-Button ms-Button--primary">
                        <span class="ms-Button-label">Login</span>
                    </button>
                    <div class="login-status" id="login-status" style="margin-top: 10px; color: #d13438; display: none;">
                        Invalid credentials. Please try again.
                    </div>
                </div>
            </form>
        </div>
    </div>

    <header class="ms-header">
        <div class="ms-header-title">
            <a class="ms-header-brand" href="#">Test Automation Framework</a>
        </div>
        <div class="ms-header-controls">
            <span class="ms-environment-display" id="environment-display">Environment: Development</span>
            <span class="ms-user-info" id="user-display">Not logged in</span>
            <button class="ms-Button ms-Button--default" id="login-button">
                <span class="ms-Button-label">Login</span>
            </button>
            <button class="ms-Button ms-Button--default" id="logout-button" style="display: none;">
                <span class="ms-Button-label">Logout</span>
            </button>
        </div>
    </header>

    <div class="ms-container">
        <div id="notification-container" style="position: fixed; top: 70px; right: 20px; z-index: 9999; width: 350px;"></div>
        <div class="ms-layout">
            <nav id="sidebarMenu" class="ms-nav">
                <div class="ms-nav-content">
                    <ul class="ms-nav-list">
                        <li class="ms-nav-item">
                            <a class="ms-nav-link ms-nav-link-active" href="#">
                                Dashboard
                            </a>
                        </li>
                        <li class="ms-nav-item">
                            <a class="ms-nav-link" href="/config/index.html">
                                Custom Test Runner
                            </a>
                        </li>
                        <li class="ms-nav-item">
                            <a class="ms-nav-link" href="/reports/index.html">
                                Reports
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="ms-content">
                <div class="ms-content-header">
                    <h1 class="ms-font-xxl">Dashboard</h1>
                    <div class="ms-content-actions">
                        <button type="button" class="ms-Button ms-Button--default" id="refresh-btn">
                            <span class="ms-Button-label">Refresh</span>
                        </button>
                    </div>
                </div>

                <div class="ms-grid">
                    <div class="ms-grid-row">
                        <div class="ms-grid-col ms-sm3">
                            <div class="ms-stat-card ms-bgColor-themePrimary">
                                <div class="ms-stat-title ms-fontColor-white">Total Runs</div>
                                <div class="ms-stat-value ms-fontColor-white" id="total-tests">0</div>
                            </div>
                        </div>
                        <div class="ms-grid-col ms-sm3">
                            <div class="ms-stat-card ms-bgColor-green">
                                <div class="ms-stat-title ms-fontColor-white">Passed</div>
                                <div class="ms-stat-value ms-fontColor-white" id="successful-tests">0</div>
                            </div>
                        </div>
                        <div class="ms-grid-col ms-sm3">
                            <div class="ms-stat-card ms-bgColor-red">
                                <div class="ms-stat-title ms-fontColor-white">Failed</div>
                                <div class="ms-stat-value ms-fontColor-white" id="failed-tests">0</div>
                            </div>
                        </div>
                        <div class="ms-grid-col ms-sm3">
                            <div class="ms-stat-card ms-bgColor-orange">
                                <div class="ms-stat-title ms-fontColor-white">IN PROGRESS</div>
                                <div class="ms-stat-value ms-fontColor-white" id="running-tests">0</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Active Tests Section -->
                <div class="ms-section-header">
                    <h2 class="ms-font-xl ms-section-title">Active Tests</h2>
                    <div class="active-tests-filter">
                        <button class="ms-Button ms-Button--default active-tests-filter-btn active" data-filter="all">
                            <span class="ms-Button-label">All Tests</span>
                        </button>
                        <button class="ms-Button ms-Button--default active-tests-filter-btn" data-filter="mine">
                            <span class="ms-Button-label">My Tests</span>
                        </button>
                        <button class="ms-Button ms-Button--default active-tests-filter-btn" data-filter="others">
                            <span class="ms-Button-label">Others' Tests</span>
                        </button>
                    </div>
                </div>
                <div id="active-tests-container" class="ms-card-container">
                    <!-- Active tests will be populated here -->
                    <div class="ms-empty-message">No active tests</div>
                </div>

                <!-- Predefined Test Suites Section -->
                <h2 class="ms-font-xl ms-section-title">Predefined Test Suites</h2>
                <!-- Custom Test Case Runner -->
                <div class="ms-custom-run-container" style="display: flex; align-items: center; margin-bottom: 16px;">
                    <input type="number" id="custom-tc-id-input" class="ms-TextField-field" placeholder="Enter Test Case ID" style="width: 150px; margin-right: 8px;">
                    <button id="run-custom-tc-button" class="ms-Button ms-Button--primary">
                        <span class="ms-Button-label">Run Test Case</span>
                    </button>
                </div>
                <div id="predefined-suites-container" class="ms-card-container">
                    <div class="ms-predefined-grid">
                        <div class="ms-predefined-card">
                            <h3 class="ms-font-l">Smoke Test</h3>
                            <p>Quick critical path verification for core system functions</p>
                            <button class="ms-Button ms-Button--primary" data-suite-id="322" data-suite-name="DEMO PE2.1 Smoke Test">
                                <span class="ms-Button-label">Run Smoke Test</span>
                            </button>
                        </div>
                        <div class="ms-predefined-card">
                            <h3 class="ms-font-l">Heartbeat Test</h3>
                            <p>Continuous monitoring tests to verify system availability</p>
                            <button class="ms-Button ms-Button--primary" data-suite-id="323" data-suite-name="DEMO PE2.1 Heartbeat Test">
                                <span class="ms-Button-label">Run Heartbeat Test</span>
                            </button>
                        </div>
                        <div class="ms-predefined-card">
                            <h3 class="ms-font-l">Sanity Test</h3>
                            <p>In-depth validation of key business and processing flows</p>
                            <button class="ms-Button ms-Button--primary" data-suite-id="312" data-suite-name="DEMO PE2.1 Sanity Test">
                                <span class="ms-Button-label">Run Sanity Test</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Test Suite Selection Section -->
                <h2 class="ms-font-xl ms-section-title">Test Suite Selection</h2>
                <div class="ms-card">
                    <div class="ms-card-content">
                        <!-- Filters Row -->
                        <div class="ms-Grid">
                            <div class="ms-Grid-row filter-controls">
                                <!-- Filter controls in a row with flex layout -->
                                <div class="ms-Grid-col ms-sm12">
                                    <div class="filter-container" style="display: flex; flex-wrap: wrap; gap: 16px; align-items: flex-end; margin-bottom: 16px;">
                                        <!-- Project Filter -->
                                        <div class="filter-item" style="flex: 1; min-width: 150px; max-width: 220px;">
                                            <div class="ms-form-group" style="margin-bottom: 0;">
                                                <label for="suite-project-filter" class="ms-Label">Project</label>
                                                <select id="suite-project-filter" class="ms-Dropdown-select" style="width: 100%;">
                                                    <option value="">All Projects</option>
                                                    <!-- Project options will be populated dynamically -->
                                                </select>
                                            </div>
                                        </div>
                                        
                                        <!-- Integration Level Filter -->
                                        <div class="filter-item" style="flex: 1; min-width: 150px; max-width: 220px;">
                                            <div class="ms-form-group" style="margin-bottom: 0;">
                                                <label for="suite-level-filter" class="ms-Label">Integration Level</label>
                                                <select id="suite-level-filter" class="ms-Dropdown-select" style="width: 100%;">
                                                    <option value="">All Levels</option>
                                                    <option value="Microservice">Microservice</option>
                                                    <option value="Integrated">Integrated</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <!-- Version Filter -->
                                        <div class="filter-item" style="flex: 1; min-width: 150px; max-width: 220px;">
                                            <div class="ms-form-group" style="margin-bottom: 0;">
                                                <label for="suite-version-filter" class="ms-Label">Version</label>
                                                <select id="suite-version-filter" class="ms-Dropdown-select" style="width: 100%;">
                                                    <option value="">All Versions</option>
                                                    <option value="2.1">2.1</option>
                                                    <option value="2.1 + Patch">2.1 + Patch</option>
                                                    <option value="3.0">3.0</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        
                        <!-- Available Test Suites -->
                        <div class="ms-form-group" style="margin-top: 20px;">
                            <label class="ms-Label">Available Test Suites</label>
                            <div id="available-testsuites" class="ms-selection-list">
                                <!-- Test suites will be loaded here dynamically -->
                                <div class="ms-empty-message">Select filters to load test suites</div>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="ms-form-actions">
                            <button id="refresh-suites" class="ms-Button">
                                <span class="ms-Button-label">Refresh</span>
                            </button>
                            <button id="run-selected-suite" class="ms-Button ms-Button--primary" disabled>
                                <span class="ms-Button-label">Run Selected Suite</span>
                            </button>
                        </div>
                    </div>
                </div>


            </main>
        </div>
    </div>

    <!-- Test Report Modal -->
    <div id="report-modal" class="ms-modal">
        <div class="ms-modal-content">
            <span class="ms-modal-close">&times;</span>
            <h2 class="ms-font-xl">Test Report</h2>
            <div id="report-content">
                <!-- Report content will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Loading Indicator -->
    <div id="loading-indicator" class="ms-loading-overlay" style="display: none;">
        <div class="ms-Spinner ms-Spinner--large"></div>
        <div class="ms-loading-message">Loading...</div>
    </div>

    <!-- Notification Container -->
    <div id="notification-container" class="ms-notification-container"></div>

    <!-- Dashboard utilities - Load in correct order with fallbacks -->
    <script>
        // Global error handler for script loading issues
        window.onerror = function(message, source, lineno, colno, error) {
            console.error('Script error:', { message, source, lineno, colno });
            if (source && source.includes('api-service.js')) {
                console.error('API Service failed to load correctly');
                alert('API Service could not be loaded correctly. Try refreshing the page.');
            }
            return false; // Allow default error handling as well
        };
    </script>

    <!-- Load scripts in proper order -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="notifications.js"></script>
    <script>
        // Fallback for notifications
        if (typeof showNotification !== 'function') {
            window.showNotification = function(title, message, type) {
                console.log(`Notification: ${title} - ${message} (${type})`);
                alert(`${title}: ${message}`);
            };
            console.log('Using fallback notification system');
        }
    </script>

    <script src="api-integration.js"></script>
    <script src="dashboard.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check for API service and initialize dashboard event listeners
            if (window.apiService) {
                console.log('Unified API Service is available, initializing dashboard listeners.');
                initDashboard(); // This only sets up listeners now, which is safe.
            } else {
                console.error('Unified API Service not found! Make sure api-service.js is loaded correctly.');
                showNotification('Error', 'Unified API Service not found. Please refresh the page.', 'error');
            }

            const loginForm = document.getElementById('login-form');
            const loginButton = document.getElementById('login-button');
            const logoutButton = document.getElementById('logout-button');

            // Login form submission handler
            if (loginForm) {
                loginForm.addEventListener('submit', async function(event) {
                    event.preventDefault();
                    const usernameInput = document.getElementById('username');
                    const passwordInput = document.getElementById('password');
                    const loginStatus = document.getElementById('login-status');

                    if (!usernameInput || !passwordInput) return;

                    const username = usernameInput.value;
                    const password = passwordInput.value;

                    if (!window.apiService) {
                        console.error('Login attempt failed: apiService is not available.');
                        showNotification('Error', 'API service is not ready. Please wait and try again.', 'error');
                        return;
                    }

                    showNotification('Logging in', 'Please wait...', 'info');
                    if(loginStatus) loginStatus.style.display = 'none';

                    try {
                        const success = await window.apiService.login(username, password);
                        if (success) {
                            // On successful login, dashboard.js will handle initialization via the 'apiservice-ready' event.
                            // We just need to handle the UI changes for login here.
                            handleSuccessfulLogin(username);
                        } else {
                            if(loginStatus) {
                                loginStatus.textContent = 'Invalid credentials. Please try again.';
                                loginStatus.style.display = 'block';
                            }
                            showNotification('Login Failed', 'Invalid credentials.', 'error');
                        }
                    } catch (error) {
                        console.error('Login error:', error);
                        if(loginStatus) {
                            loginStatus.textContent = 'An error occurred during login.';
                            loginStatus.style.display = 'block';
                        }
                        showNotification('Login Error', 'An unexpected error occurred.', 'error');
                    }
                });
            }

            // Button handlers
            if(loginButton) {
                loginButton.addEventListener('click', showLoginModal);
            }
            if(logoutButton) {
                logoutButton.addEventListener('click', handleLogout);
            }
        });
    </script>
</body>
</html>