/**
 * Test Case Queries
 * Provides functions for querying test cases
 */
const QueryBuilder = require('../utils/query-builder');
const formatter = require('../utils/result-formatter');

/**
 * Get test cases with optional filtering
 * @param {Object} connection - Database connection
 * @param {Object} filters - Optional filters
 * @returns {Promise<Array>} - Test cases
 */
async function getTestCases(connection, filters = {}) {
  console.log('🔍 [QUERY] getTestCases called with filters:', JSON.stringify(filters, null, 2));
  const { ts_id, status, limit = 50, cacheKey } = filters;
  console.log('🔍 [QUERY] Extracted parameters:', { ts_id, status, limit, cacheKey });

  // Use cache if available to improve performance
  if (global.testCasesCache && global.testCasesCache[cacheKey || 'default']) {
    console.log('✅ [QUERY] Using cached test cases data');
    const cachedData = global.testCasesCache[cacheKey || 'default'];
    console.log('✅ [QUERY] Cached data length:', cachedData ? cachedData.length : 'null/undefined');
    return cachedData;
  }

  // Create query builder
  const queryBuilder = new QueryBuilder();

  // Base query - retrieve all essential fields for search results
  queryBuilder.select('test_case tc', [
    'tc.tc_id',
    'tc.name',
    'tc.status',
    'tc.uid',
    'tc.comments',
    'tc.case_driver',
    'tc.tp_id',
    'tc.tickets'
  ]);

  // Filter by suite ID if provided
  if (ts_id) {
    queryBuilder.join('test_case_group tcg', 'tc.tc_id = tcg.tc_id');
    queryBuilder.where('tcg.ts_id', '=', ts_id);
  }

  // Filter by status if provided
  if (status) {
    queryBuilder.where('tc.status', '=', status);
  }

  // Add ordering and limit
  queryBuilder.orderBy('tc.tc_id', 'DESC');
  queryBuilder.limit(limit);

  // Build and execute query
  const { sql, params } = queryBuilder.build();
  console.log('🔍 [QUERY] Built SQL query:', sql);
  console.log('🔍 [QUERY] Query parameters:', params);

  try {
    console.log('🔄 [QUERY] Executing query...');
    console.time('testCasesQuery');
    const rows = await connection.query(sql, params);
    console.timeEnd('testCasesQuery');

    console.log('✅ [QUERY] Raw database rows returned:');
    console.log('   - Type:', typeof rows);
    console.log('   - Is Array:', Array.isArray(rows));
    console.log('   - Length:', rows ? rows.length : 'null/undefined');
    console.log('   - First 3 raw rows:', rows ? rows.slice(0, 3) : 'none');

    console.log('🔄 [QUERY] Formatting results...');
    const formattedResults = formatter.formatTestCases(rows);
    console.log('✅ [QUERY] Formatted results:');
    console.log('   - Type:', typeof formattedResults);
    console.log('   - Is Array:', Array.isArray(formattedResults));
    console.log('   - Length:', formattedResults ? formattedResults.length : 'null/undefined');
    console.log('   - First 3 formatted items:', formattedResults ? formattedResults.slice(0, 3) : 'none');

    // Cache the results for future requests
    if (!global.testCasesCache) {
      global.testCasesCache = {};
    }
    global.testCasesCache[cacheKey || 'default'] = formattedResults;
    console.log('💾 [QUERY] Results cached with key:', cacheKey || 'default');

    // Set a timeout to clear the cache after 5 minutes
    setTimeout(() => {
      if (global.testCasesCache && global.testCasesCache[cacheKey || 'default']) {
        delete global.testCasesCache[cacheKey || 'default'];
        console.log('Test cases cache cleared');
      }
    }, 5 * 60 * 1000);

    return formattedResults;
  } catch (error) {
    console.error('❌ [QUERY] Error executing getTestCases query:', error);
    console.error('❌ [QUERY] Error stack:', error.stack);

    // Try a simpler fallback query if the main query fails
    try {
      console.log('🔄 [QUERY] Attempting fallback query for test cases');
      const fallbackSql = `
        SELECT tc_id, uid, status, name, comments, case_driver, tp_id, tickets
        FROM test_case
        ORDER BY tc_id DESC
        LIMIT ?
      `;
      console.log('🔍 [QUERY] Fallback SQL:', fallbackSql);
      console.log('🔍 [QUERY] Fallback params:', [limit]);

      console.time('fallbackQuery');
      const fallbackRows = await connection.query(fallbackSql, [limit]);
      console.timeEnd('fallbackQuery');

      console.log('✅ [QUERY] Fallback query returned:');
      console.log('   - Type:', typeof fallbackRows);
      console.log('   - Is Array:', Array.isArray(fallbackRows));
      console.log('   - Length:', fallbackRows ? fallbackRows.length : 'null/undefined');
      console.log('   - First 3 fallback rows:', fallbackRows ? fallbackRows.slice(0, 3) : 'none');

      const fallbackFormatted = formatter.formatTestCases(fallbackRows);
      console.log('✅ [QUERY] Fallback formatted results:');
      console.log('   - Length:', fallbackFormatted ? fallbackFormatted.length : 'null/undefined');

      return fallbackFormatted;
    } catch (fallbackError) {
      console.error('❌ [QUERY] Fallback query also failed:', fallbackError);
      console.error('❌ [QUERY] Fallback error stack:', fallbackError.stack);
      return [];
    }
  }
}

/**
 * Get a test case by ID
 * @param {Object} connection - Database connection
 * @param {number|string} tc_id - Test case ID
 * @returns {Promise<Object>} - Test case
 */
async function getTestCaseById(connection, tc_id) {
  // Create query builder
  const queryBuilder = new QueryBuilder();

  // Build query
  queryBuilder.select('test_case', [
    'tc_id',
    'uid',
    'status',
    'case_driver',
    'tp_id',
    'comments',
    'tickets',
    'name'
  ]);
  queryBuilder.where('tc_id', '=', tc_id);

  // Build and execute query
  const { sql, params } = queryBuilder.build();

  try {
    const rows = await connection.query(sql, params);

    if (rows.length === 0) {
      throw new Error(`Test case with ID ${tc_id} not found`);
    }

    return formatter.formatTestCases(rows)[0];
  } catch (error) {
    console.error(`Error getting test case by ID ${tc_id}:`, error);
    throw error;
  }
}

/**
 * Search test cases
 * @param {Object} connection - Database connection
 * @param {Object} criteria - Search criteria
 * @returns {Promise<Array>} - Test cases
 */
async function searchTestCases(connection, criteria = {}) {
  const { name, status, min_id, max_id, comments, limit = 20 } = criteria;

  // Create query builder
  const queryBuilder = new QueryBuilder();

  // Base query
  queryBuilder.select('test_case', [
    'tc_id',
    'uid',
    'status',
    'case_driver',
    'tp_id',
    'comments',
    'tickets',
    'name'
  ]);

  // Add filters
  if (name) {
    queryBuilder.where('name', 'LIKE', `%${name}%`);
  }

  if (status) {
    queryBuilder.where('status', '=', status);
  }

  if (min_id) {
    queryBuilder.where('tc_id', '>=', min_id);
  }

  if (max_id) {
    queryBuilder.where('tc_id', '<=', max_id);
  }

  // Add filter for comments if provided
  if (comments) {
    queryBuilder.where('comments', 'LIKE', `%${comments}%`);
  }

  // Add ordering and limit
  queryBuilder.orderBy('tc_id', 'DESC');
  queryBuilder.limit(limit);

  // Build and execute query
  const { sql, params } = queryBuilder.build();

  try {
    const rows = await connection.query(sql, params);
    return formatter.formatTestCases(rows);
  } catch (error) {
    console.error('Error searching test cases:', error);
    return [];
  }
}

module.exports = {
  getTestCases,
  getTestCaseById,
  searchTestCases
};
