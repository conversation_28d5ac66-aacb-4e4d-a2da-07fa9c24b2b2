async function loadTestDetails(testId) {
    try {
        console.log(`Loading details for test ${testId}...`);

        // Show loading indicator
        if (elements.testDetailsSection) {
            elements.testDetailsSection.classList.remove('d-none');
            // Scroll to the details section
            elements.testDetailsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });

            if (elements.testDetailsTitle) {
                elements.testDetailsTitle.textContent = `Loading Test Details...`;
            }
        } else {
            console.error('Test details section element not found');
            return;
        }

        // Get credentials from the unified API flow
        let credentials = null;

        // First try to get from window.apiService (set during dashboard login)
        if (window.apiService && window.apiService.credentials && window.apiService.credentials.uid) {
            credentials = window.apiService.credentials;
            console.log('Using credentials from API service:', credentials.uid);
        } else {
            // Try to get credentials from session storage as fallback
            const sessionUid = sessionStorage.getItem('smarttest_uid');
            const sessionPwd = sessionStorage.getItem('smarttest_pwd');
            if (sessionUid && sessionPwd) {
                credentials = { uid: sessionUid, password: sessionPwd };
                console.log('Using credentials from session storage:', sessionUid);
            } else {
                console.log('No credentials found in session storage');
            }
        }
        
        // Log credential status (password masked)
        if (credentials && credentials.uid) {
            console.log('Found credentials for user ID:', credentials.uid);
            console.log('Password length:', credentials.password ? credentials.password.length : 0);
        } else {
            console.warn('No credentials found - authentication will likely fail');
        }
        
        // Ensure credentials are properly set in both API services
        if (window.externalApiService || window.enhancedExternalApiService) {
            const externalApiService = window.externalApiService || window.enhancedExternalApiService;
            
            // ExternalApiService inherits setCredentials from BaseApiService
            if (credentials && credentials.uid && credentials.password) {
                console.log('Setting credentials in externalApiService for:', credentials.uid);
                
                // Set credentials using the inherited method from BaseApiService
                if (typeof externalApiService.setCredentials === 'function') {
                    externalApiService.setCredentials(credentials.uid, credentials.password);
                    console.log('✅ Credentials set successfully in externalApiService');
                } else {
                    console.warn('⚠️ setCredentials method not available on externalApiService');
                    // As fallback, set the credentials directly on the object
                    externalApiService.credentials = credentials;
                    console.log('✅ Credentials set directly on externalApiService object');
                }
            } else {
                console.warn('⚠️ No valid credentials available to set in externalApiService');
            }
        }

        // Find basic test info in current state - will be used only as a fallback
        const basicTestInfo = currentState.reports.find(r => r.id === testId || r.tsn_id === testId);
        let testDetails = null;

        try {
            // DIAGNOSTIC: Check what services are available
            console.log('[DIAGNOSTIC] Available services check:');
            console.log('- window.externalApiService:', !!window.externalApiService);
            console.log('- window.enhancedExternalApiService:', !!window.enhancedExternalApiService);
            console.log('- window.apiService:', !!window.apiService);

            // EXTERNAL API ONLY: Use external API exclusively, no database fallback
            const externalApiService = window.externalApiService || window.enhancedExternalApiService;

            if (externalApiService) {
                console.log('🚀 USING External API Service ONLY for test details (no database fallback)');
                console.log('Service type:', window.externalApiService ? 'externalApiService' : 'enhancedExternalApiService');

                try {
                    const details = await loadTestDetailsFromExternalApi(testId, credentials);
                    console.log('[DEBUG] loadTestDetails: External API details response:', details);

                    if (details && details.tsn_id) {
                        let finalDetailsToStore = details; // Start with the original details object
                        const tsnIdForEnhancement = details.tsn_id;

                        // The 'details.report' field from loadTestDetailsFromExternalApi 
                        // is assumed to be the "Detailed Step Table HTML" string.
                        const detailedStepTableHtmlString = details.report; 

                        if (detailedStepTableHtmlString && typeof detailedStepTableHtmlString === 'string') {
                            console.log('[Enhance] Using details.report as the source for Detailed Step Table HTML.');
                            try {
                                // Generate the new report HTML string using the existing details.report 
                                // and original test cases from the 'details' object.
                                const newReportHtml = generateEnhancedReportHtml(detailedStepTableHtmlString, details.test_cases, tsnIdForEnhancement);
                                
                                // Create a new details object to include the new report HTML
                                finalDetailsToStore = { 
                                    ...details, // Spread all properties from the original 'details' object
                                    report: newReportHtml  // Override the 'report' property with our enhanced HTML
                                };
                                console.log('[Enhance] Successfully generated and integrated enhanced report HTML using existing details.report.');
                            } catch (generationError) {
                                console.error('[Enhance] Error during HTML generation from details.report:', generationError);
                                // finalDetailsToStore remains the original 'details' if generation fails
                            }
                        } else {
                            console.warn(`[Enhance] details.report is empty, not a string, or undefined. Cannot enhance. Original structure will be used.`);
                            // finalDetailsToStore remains the original 'details'
                        }
                        
                        currentState.currentTestDetails = finalDetailsToStore;
                        console.log('✅ Successfully processed and stored test details into currentState.');
                    } else {
                        console.error('❌ External API returned invalid/empty details');
                        throw new Error('External API returned invalid or empty test details');
                    }
                } catch (externalApiError) {
                    console.error('❌ External API failed:', externalApiError);
                    console.error('🚫 NO FALLBACK - External API must work for test details');
                    throw new Error(`External API failed: ${externalApiError.message}`);
                }
            } else {
                console.error('❌ No External API Service available');
                console.error('🚫 Database API fallback is DISABLED - External API service is required');
                throw new Error('External API Service is required but not available. Please ensure external-api-service.js is loaded.');
            }

            // Get the updated test details from current state
            testDetails = currentState.currentTestDetails;

            // If still not found, fall back to basic info (if available)
            if (!testDetails && basicTestInfo) {
                console.log('[DEBUG] loadTestDetails: Falling back to basic test info from cache');
                testDetails = basicTestInfo;
                currentState.currentTestDetails = testDetails;
            }

            // If still not found, show error
            if (!testDetails) {
                if (elements.testDetailsTitle) {
                    elements.testDetailsTitle.textContent = `Test Details Not Found`;
                }
                return;
            }
        } catch (error) {
            console.error(`Error fetching test details:`, error);
            if (elements.testDetailsTitle) {
                elements.testDetailsTitle.textContent = `Error Loading Test Details: ${error.message}`;
            }
            return;
        }

        // Make sure we have a test cases array
        if (!testDetails.test_cases || testDetails.test_cases.length === 0) {
            // Create mock test cases if they're missing
            testDetails.test_cases = [];
            if (testDetails.tc_id) {
                // This is a single test case run
                testDetails.test_cases.push({
                    tc_id: testDetails.tc_id,
                    description: testDetails.test_name || testDetails.name || `Test Case ${testDetails.tc_id}`,
                    status: testDetails.status || 'Unknown',
                    duration: testDetails.duration || '0:00',
                    error_message: testDetails.error_message || ''
                });
            } else if (testDetails.test_id || testDetails.ts_id) {
                // This is a test suite that should have test cases
                // Try to extract test cases from any other available fields
                // testDetails.test_cases = []; // Already initialized above

                // Try to calculate total cases from passed/failed/skipped counts
                const totalCases = (testDetails.passed_cases || 0) + (testDetails.failed_cases || 0) + (testDetails.skipped_cases || 0);

                if (totalCases > 0) {
                    // We know how many cases there are, but not their details
                    // Create a placeholder entry
                    // Commented out the 'Multiple' placeholder push:
                    // testDetails.test_cases.push({
                    //     tc_id: 'Multiple',
                    //     description: `This test suite contains ${totalCases} test cases`,
                    //     status: 'See summary',
                    //     duration: testDetails.duration || '0:00',
                    //     error_message: testDetails.error || '' // Use main session error for placeholder
                    // });
                }
            }
        }

        // Update UI
        displayTestDetails();

        // If the test has test_cases, update the test cases table
        if (testDetails.test_cases && testDetails.test_cases.length > 0) {
            updateTestCasesTable(testDetails.test_cases);
        } else {
            // If no test cases are available, show empty table with clear message
            updateTestCasesTable([]);
        }

        console.log('Test details loaded:', testDetails);
    } catch (error) {
        console.error('Error loading test details:', error);
        if (elements.testDetailsTitle) {
            elements.testDetailsTitle.textContent = `Error Loading Test Details`;
        }
    }
}

/**
 * Load test details from the external API directly
 * @param {string} testId - Test session ID
 * @param {Object} credentials - User credentials
 */
