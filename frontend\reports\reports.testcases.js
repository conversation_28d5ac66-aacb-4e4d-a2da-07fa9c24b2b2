function updateTestCasesTableHeaders() {
    const table = document.getElementById('test-cases-table');
    if (!table) return;
    
    // First, completely remove the existing thead to avoid duplication issues
    let thead = table.querySelector('thead');
    if (thead) {
        thead.remove();
    }
    
    // Create a new thead element
    thead = document.createElement('thead');
    table.insertBefore(thead, table.querySelector('tbody'));
    
    // Create the header row with proper structure
    const headerRow = document.createElement('tr');
    
    // Checkbox column
    const checkboxHeader = document.createElement('th');
    checkboxHeader.scope = 'col';
    checkboxHeader.className = 'text-center';
    const selectAllCheckbox = document.createElement('input');
    selectAllCheckbox.type = 'checkbox';
    selectAllCheckbox.id = 'selectAllTestCasesCheckbox';
    selectAllCheckbox.className = 'form-check-input';
    selectAllCheckbox.title = 'Select/Deselect All';
    selectAllCheckbox.onchange = handleSelectAllCheckboxChange;
    checkboxHeader.appendChild(selectAllCheckbox);
    headerRow.appendChild(checkboxHeader);
    
    // Add other column headers
    const columns = ['Case ID', 'Status', 'Duration', 'Error Message'];
    columns.forEach(column => {
        const th = document.createElement('th');
        th.scope = 'col';
        th.textContent = column;
        headerRow.appendChild(th);
    });
    
    // Add the header row to thead
    thead.appendChild(headerRow);
    
    console.log('Updated test cases table headers with clean structure');
}

// Update the test cases table with corrected column structure
function updateTestCasesTable(testCases) {
    // First, update the table headers to match our new structure
    updateTestCasesTableHeaders();
    const table = document.getElementById('test-cases-table');
    const tbody = table.querySelector('tbody');

    // Initialize bulk rerun UI if not already done
    if (!currentState.bulkRerunUIInitialized) {
        initializeBulkRerunUI();
    }
    
    // Clear the table
    tbody.innerHTML = '';

    // Check if we have test cases
    if (!testCases || testCases.length === 0) {
        // Add a row with a message
        const row = document.createElement('tr');
        row.innerHTML = `
            <td colspan="6" class="text-center">
                <div class="py-3">
                    <i class="fas fa-search me-2"></i>
                    No test cases found for this test run.
                </div>
            </td>
        `;
        tbody.appendChild(row);
        
        // Disable the bulk rerun button since no test cases are available
        const rerunBtn = document.getElementById('rerunSelectedCasesBtnGlobal');
        if (rerunBtn) {
            rerunBtn.disabled = true;
        }
        
        return;
    }

    console.log(`Found ${testCases.length} test cases`);

    testCases.forEach(testCase => {
        // Create main data row
        const mainRow = document.createElement('tr');
        mainRow.className = 'test-case-main-row';

        // Format status text
        const status = testCase.status || (testCase.outcome === 'P' ? 'Passed' : testCase.outcome === 'F' ? 'Failed' : testCase.outcome || 'Unknown');
        const statusLower = status.toLowerCase();
        const statusClass = (statusLower.includes('pass') || statusLower === 'success') ? 'text-success' : (statusLower.includes('fail') || statusLower === 'failure') ? 'text-danger' : 'text-warning';
        
        // Add Checkbox Cell (first column) - using rowspan=2 to span both rows
        const checkboxCell = document.createElement('td');
        checkboxCell.className = 'text-center align-middle';
        checkboxCell.setAttribute('rowspan', '2');
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.className = 'form-check-input test-case-checkbox';
        checkbox.dataset.tcId = testCase.tc_id || testCase.id;
        const parentTsnId = currentState.currentTestDetails?.tsn_id || currentState.currentTestDetails?.id;
        checkbox.dataset.tsnId = parentTsnId;
        checkbox.onchange = handleRowCheckboxChange;
        checkboxCell.appendChild(checkbox);
        mainRow.appendChild(checkboxCell);

        // TC ID Cell (now separate from description)
        const idCell = document.createElement('td');
        idCell.className = 'align-middle';
        idCell.innerHTML = `<strong>${testCase.tc_id || testCase.id || ''}</strong>`;
        mainRow.appendChild(idCell);

        // Status Cell
        const statusCell = document.createElement('td');
        statusCell.innerHTML = `<span class="${statusClass}">${status}</span>`;
        mainRow.appendChild(statusCell);

        // Create Date Cell
        const createDateCell = document.createElement('td');
        if (testCase.creation_time) {
            createDateCell.setAttribute('data-original-time', testCase.creation_time);
            createDateCell.textContent = formatDateTime(testCase.creation_time);
        } else {
            createDateCell.textContent = 'N/A';
        }
        mainRow.appendChild(createDateCell);

        // Create Error/Details Cell
        let errorCell = createErrorCell(testCase);
        mainRow.appendChild(errorCell);
        
        // Add the main row to the table
        tbody.appendChild(mainRow);
        
        // Create description row (second row)
        const descriptionRow = document.createElement('tr');
        descriptionRow.className = 'test-case-description-row';
        
        // Description cell that spans all remaining columns
        const descriptionCell = document.createElement('td');
        descriptionCell.setAttribute('colspan', '5'); // Spans all columns except checkbox
        descriptionCell.className = 'border-top-0 pt-0';
        
        // Combine test case name and description
        const testCaseName = testCase.test_case_name || testCase.name || '';
        const testCaseDesc = testCase.description || '';
        const displayText = testCaseName ? (testCaseDesc ? `${testCaseName} - ${testCaseDesc}` : testCaseName) : testCaseDesc;
        
        descriptionCell.innerHTML = `<small class="text-muted">${displayText}</small>`;
        descriptionRow.appendChild(descriptionCell);
        
        // Add the description row to the table
        tbody.appendChild(descriptionRow);
    });

    // Update the bulk rerun button state
    updateBulkRerunButtonState();

    console.log(`Updated test cases table with ${testCases.length} cases`);
}
function exportReports() {
    // Generate CSV content
    let csvContent = 'data:text/csv;charset=utf-8,';

    // Add headers
    csvContent += 'Test ID,Type,Environment,Status,Start Time,Duration,Pass Rate\n';

    // Add rows
    currentState.reports.forEach(report => {
        const passRate = report.totalCases > 0
            ? Math.round((report.passedCases / report.totalCases) * 100)
            : 0;

        const row = [
            report.id,
            report.type,
            report.environment,
            report.status,
            new Date(report.startTime).toLocaleString(),
            report.duration || 'N/A',
            `${passRate}%`
        ];

        // Escape any commas in the data
        const escapedRow = row.map(field => {
            if (typeof field === 'string' && field.includes(',')) {
                return `"${field}"`;
            }
            return field;
        });

        csvContent += escapedRow.join(',') + '\n';
    });

    // Create download link
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement('a');
    link.setAttribute('href', encodedUri);
    link.setAttribute('download', `test_reports_${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);

    // Trigger download
    link.click();

    // Clean up
    document.body.removeChild(link);
}

/**
 * Initialize the DataTable for the reports table
 */
function initializeDataTable() {
    try {
        console.log('Initializing DataTable for reports');

        if ($.fn.dataTable.isDataTable('#reports-table')) {
            $('#reports-table').DataTable().destroy();
            console.log('Destroyed existing DataTable instance');
        }

        reportsDataTable = $('#reports-table').DataTable({
            pageLength: 25, // Initial page length
            order: [[0, 'desc']], // Default sort by first column (ID/tsn_id) descending
            responsive: true,
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]], // Options for number of records per page
            columnDefs: [
                { type: 'date', targets: [4, 5] }, // 5th and 6th columns are dates (Start/End Time)
                // Duration column (7th column, index 6) is now not orderable (disabled sorting)
                { orderable: false, targets: [6, 10] } // Both Duration and Actions columns are not orderable
            ],
            language: {
                search: "_INPUT_",
                searchPlaceholder: "Search reports...",
                lengthMenu: "Show _MENU_ records",
                info: "Showing _START_ to _END_ of _TOTAL_ entries" // _TOTAL_ will be based on client-side data count
            }
        });

        console.log('DataTable initialized successfully');

        // The 'length.dt' event that previously called loadReportsData is REMOVED.
        // DataTable will handle length changes purely on the client-side data.

        setupCustomFilters();

    } catch (error) {
        console.error('Error initializing DataTable:', error);
    }
}

// Modified setupCustomFilters function for c:\Dev\smarttest\frontend\reports\reports.js

/**
 * Set up custom filtering for the reports table and update charts accordingly.
 */
