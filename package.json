{"name": "smarttest", "version": "1.0.0", "description": "SmartTest Application", "main": "index.js", "scripts": {"start": "node frontend/server/api.js", "dev": "nodemon frontend/server/api.js", "build": "npm run clean && npm run build:dashboard && npm run build:config && npm run build:reports", "build:dashboard": "npx copyfiles -u 2 frontend/dashboard/**/* frontend/server/public/dashboard", "build:config": "npx copyfiles -u 2 frontend/config/**/* frontend/server/public/config", "build:reports": "npx copyfiles -u 2 frontend/reports/**/* frontend/server/public/reports", "clean": "npx rimraf frontend/server/public", "test": "jest", "test:all": "jest --projects .", "test:coverage": "jest --coverage", "test:reports": "jest --selectProjects reports", "test:server": "jest --selectProjects server", "test:reports:coverage": "jest --selectProjects reports --coverage", "test:server:coverage": "jest --selectProjects server --coverage", "test:unified": "jest --config tests/unified/jest.config.js", "test:unified:unit": "jest --config tests/unified/jest.config.js --selectProjects 'Unit Tests'", "test:unified:integration": "jest --config tests/unified/jest.config.js --selectProjects 'Integration Tests'", "test:unified:e2e": "jest --config tests/unified/jest.config.js --selectProjects 'E2E Tests'", "test:unified:coverage": "jest --config tests/unified/jest.config.js --coverage", "test:unified:watch": "jest --config tests/unified/jest.config.js --watch", "test:unified:ci": "jest --config tests/unified/jest.config.js --ci --coverage --watchAll=false", "test:database": "node tests/unified/run-database-tests.js all", "test:database:integration": "node tests/unified/run-database-tests.js database", "test:database:api": "node tests/unified/run-database-tests.js api-database", "test:database:verbose": "DB_LOG_LEVEL=verbose node tests/unified/run-database-tests.js all"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "mysql2": "^3.14.1", "node-fetch": "^3.3.2", "ssh2": "^1.16.0", "uuid": "^10.0.0"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "copyfiles": "^2.4.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jsdom": "^26.1.0", "nodemon": "^2.0.22", "rimraf": "^5.0.10", "supertest": "^7.1.1"}, "jest": {"testEnvironment": "jsdom"}}