/**
 * formatters.js
 *
 * This module provides a collection of utility functions for formatting data
 * consistently across the dashboard UI.
 */

/**
 * Formats the time difference between a start and end date.
 * @param {string | Date} startTime - The start time.
 * @param {string | Date} endTime - The end time.
 * @returns {string} The formatted duration (e.g., "1h 2m 3s").
 */
function formatDuration(startTime, endTime) {
    if (!startTime || !endTime) return 'N/A';
    const start = new Date(startTime);
    const end = new Date(endTime);
    if (isNaN(start) || isNaN(end)) return 'Invalid date';

    let diff = Math.abs(end - start) / 1000; // Difference in seconds

    const hours = Math.floor(diff / 3600);
    diff %= 3600;
    const minutes = Math.floor(diff / 60);
    const seconds = Math.floor(diff % 60);

    const parts = [];
    if (hours > 0) parts.push(`${hours}h`);
    if (minutes > 0) parts.push(`${minutes}m`);
    if (seconds > 0 || parts.length === 0) parts.push(`${seconds}s`);

    return parts.join(' ');
}

/**
 * Returns a CSS class based on the test status.
 * @param {string} status - The test status (e.g., 'passed', 'failed').
 * @returns {string} The corresponding CSS class.
 */
function getStatusClass(status) {
    if (typeof status !== 'string') return 'text-muted';
    switch (status.toLowerCase()) {
        case 'passed':
        case 'completed':
            return 'text-success';
        case 'failed':
        case 'error':
            return 'text-danger';
        case 'running':
            return 'text-primary';
        case 'stopped':
            return 'text-warning';
        default:
            return 'text-muted';
    }
}

/**
 * Formats a user's email, extracting the name part.
 * @param {string} email - The user's email address.
 * @returns {string} The formatted user name.
 */
function formatUserEmail(email) {
    if (!email || typeof email !== 'string') return 'Unknown';
    return email.split('@')[0];
}

/**
 * Formats a date string into a more readable format.
 * @param {string | Date} dateString - The date to format.
 * @returns {string} The formatted date string.
 */
function formatDateTime(dateString) {
    if (!dateString) return 'N/A';
    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return 'Invalid Date';
        return date.toLocaleString();
    } catch (e) {
        return 'N/A';
    }
}

export const formatters = {
    formatDuration,
    getStatusClass,
    formatUserEmail,
    formatDateTime,
};
