/**
 * Recent Runs Routes
 */
const express = require('express');
const router = express.Router();
const db = require('../database');
const { validateCredentials } = require('../middleware/auth');

// Get recent test runs
router.get('/recent-runs', validateCredentials, async (req, res) => {
  try {
    console.log('GET /local/recent-runs');

    // Check if database is disabled
    if (process.env.DB_DISABLED === 'true') {
      console.log('Database is disabled, returning empty recent runs list');
      return res.json({
        success: true,
        data: [],
        message: 'Recent runs not available - database disabled'
      });
    }

    // Process query parameters
    const { limit, since_id, uid, time_range, tsn_id, type, status } = req.query;

    // Build filters object
    const filters = {};

    // Add filters if provided
    if (limit !== undefined) {
      // Handle the 'All' option (-1) by using a very large limit
      const parsedLimit = parseInt(limit, 10);
      filters.limit = parsedLimit === -1 ? 10000 : parsedLimit;
    }

    // Add type filter for single test cases vs suites
    if (type === 'single_case') {
      filters.type = 'single_case';
    }

    // Add status filter for active tests
    if (status) {
      filters.status = status.split(','); // Support comma-separated statuses
    }

    if (since_id !== undefined) {
      const parsedSinceId = parseInt(since_id, 10);
      if (!isNaN(parsedSinceId)) {
        filters.since_id = parsedSinceId;
        console.log(`[recent-runs] Filtering with since_id: ${parsedSinceId}`);
      } else {
        console.warn(`[recent-runs] Invalid since_id provided: ${since_id}, ignoring filter`);
      }
    }

    if (uid !== undefined) {
      filters.uid = uid;
    }

    if (time_range !== undefined) {
      filters.time_range = time_range;
    }

    if (tsn_id !== undefined) {
      filters.tsn_id = tsn_id;
    }

    console.log('Fetching recent runs with filters:', filters);

    // Use the database module to fetch recent runs.
    // The result is an object, e.g., { runs: [], totalRecords: 0, ... }
    const recentRunsResult = await db.getRecentRuns(filters);

    // The frontend expects the `data` property to be an array of runs.
    // The database function can also return a raw array on some paths (e.g., no results).
    const runs = Array.isArray(recentRunsResult) ? recentRunsResult : (recentRunsResult && recentRunsResult.runs) ? recentRunsResult.runs : [];

    console.log(`Retrieved ${runs.length} recent runs`);

    // Return as JSON with success flag
    return res.json({
      success: true,
      data: runs,
      message: 'Recent runs retrieved successfully'
    });
  } catch (error) {
    console.error('Error retrieving recent runs:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve recent runs',
      error: error.message
    });
  }
});

module.exports = router;
