// Custom Test Runner JavaScript

// DOM Elements
const elements = {
    // Test runner controls
    testCaseIdInput: document.getElementById('test-case-id'),
    runTestButton: document.getElementById('run-test-btn'),
    projectSelect: document.getElementById('project-select'),
    shellHostSelect: document.getElementById('shell-host-select'),
    environmentDisplay: document.getElementById('environment-display'),
    
    // Active tests panel
    activeTestsPanel: document.getElementById('active-tests-panel'),
    activeTestsCount: document.getElementById('active-tests-count'),
    
    // Test status elements
    totalTestsCounter: document.getElementById('total-tests'),
    passedTestsCounter: document.getElementById('passed-tests'),
    failedTestsCounter: document.getElementById('failed-tests'),
    
    // Recent runs table
    recentRunsTable: document.getElementById('recent-runs-table'),
    recentRunsBody: document.getElementById('recent-runs-body'),
    environmentConfigs: {
        development: document.getElementById('development-config'),
        staging: document.getElementById('staging-config'),
        production: document.getElementById('production-config')
    },
    n8nBaseUrl: document.getElementById('n8n-base-url'),
    testExecutionWebhook: document.getElementById('test-execution-webhook'),
    nlpWebhook: document.getElementById('nlp-webhook'),
    testResultsWebhook: document.getElementById('test-results-webhook'),
    refreshInterval: document.getElementById('refresh-interval'),
    enableNotifications: document.getElementById('enable-notifications'),
    autoExpandDetails: document.getElementById('auto-expand-details'),
    // Test configuration elements
    smokeTestTimeout: document.getElementById('smoke-test-timeout'),
    smokeTestRetries: document.getElementById('smoke-test-retries'),
    regressionTestTimeout: document.getElementById('regression-test-timeout'),
    regressionTestRetries: document.getElementById('regression-test-retries'),
    heartbeatTestTimeout: document.getElementById('heartbeat-test-timeout'),
    heartbeatTestFrequency: document.getElementById('heartbeat-test-frequency'),
    heartbeatAutoEnable: document.getElementById('heartbeat-auto-enable'),
    // Environment specific elements
    devDbHost: document.getElementById('dev-db-host'),
    devDbName: document.getElementById('dev-db-name'),
    stagingDbHost: document.getElementById('staging-db-host'),
    stagingDbName: document.getElementById('staging-db-name'),
    prodDbHost: document.getElementById('prod-db-host'),
    prodDbName: document.getElementById('prod-db-name')
};

// App state for test runner
let appState = {
    activeTests: new Map(), // Map of active tests by tsn_id
    recentRunsCache: [], // Cache of recent runs data
    pollInterval: null, // Interval for polling recent runs
    credentials: { // User credentials (should be loaded from session/local storage)
        uid: '',
        password: ''
    },
    testRunDefaults: {
        envir: 'qa02',
        shell_host: 'jps-qa10-app01'
    }
};

// Performance optimization modules will be loaded via script tags
// See performance/init-performance.js for initialization

// Fallback function if performance optimizations are not loaded
function initializePerformanceOptimizations() {
    if (typeof window.initializePerformanceOptimizations === 'function') {
        window.initializePerformanceOptimizations();
        console.log('✅ Performance optimizations initialized');
    } else {
        console.warn('⚠️ Performance optimizations not available - include performance/load-performance.js');
    }
}

// Optimized refresh intervals (managed by PollingCoordinator)
const ACTIVE_TESTS_INTERVAL = 2000; // 2 seconds for active tests (15 sec to 5-6 min tests)
const RECENT_RUNS_INTERVAL = 8000; // 8 seconds for recent runs history

// Grace period for keeping completed tests visible (milliseconds)
const COMPLETED_TEST_GRACE_PERIOD = 10000;

// Initialize Custom Test Runner functionality
function initCustomTestRunner() {
    console.log('🚀 initCustomTestRunner called');

    // Set up event listeners for test runner controls
    setupEventListeners();

    // Load user credentials from localStorage
    loadCredentials();

    // Initialize test counts
    resetTestCounters();

    // Initialize performance optimizations (if available)
    initializePerformanceOptimizations();

    // Initialize status bar
    if (window.TestStatusBar) {
        window.testStatusBar = new window.TestStatusBar();
        console.log('✅ Status bar initialized');
    } else {
        console.warn('⚠️ TestStatusBar class not available');
    }

    // Start polling for recent runs only after API service is ready
    console.log('🔍 Checking API service availability...');
    console.log('window.apiService:', window.apiService);

    if (window.apiService) {
        console.log('✅ API service already available, setting credentials and starting polling immediately...');
        // Set credentials in API service
        if (appState.credentials.uid && appState.credentials.password) {
            console.log('🔑 Setting credentials:', appState.credentials.uid);
            window.apiService.setCredentials(appState.credentials.uid, appState.credentials.password);
        }
        startPolling();
    } else {
        console.log('⏳ API service not ready, waiting for apiservice-ready event...');
        // Wait for API service to be ready
        document.addEventListener('apiservice-ready', () => {
            console.log('✅ API service ready event received, setting credentials and starting polling...');
            // Set credentials in API service
            if (appState.credentials.uid && appState.credentials.password) {
                console.log('🔑 Setting credentials:', appState.credentials.uid);
                window.apiService.setCredentials(appState.credentials.uid, appState.credentials.password);
            }
            startPolling();
        });

        // Also try to start polling after a delay as fallback
        setTimeout(() => {
            console.log('⏰ Fallback timeout triggered, checking API service...');
            console.log('window.apiService:', window.apiService);
            console.log('appState.pollInterval:', appState.pollInterval);

            if (window.apiService && !appState.pollInterval) {
                console.log('✅ Fallback: Setting credentials and starting polling after delay...');
                // Set credentials in API service
                if (appState.credentials.uid && appState.credentials.password) {
                    console.log('🔑 Setting credentials:', appState.credentials.uid);
                    window.apiService.setCredentials(appState.credentials.uid, appState.credentials.password);
                }
                startPolling();
            } else {
                console.log('❌ Fallback: API service still not available or polling already started');
            }
        }, 2000);
    }
}

// Load user credentials from localStorage or session storage
function loadCredentials() {
    try {
        const storedCredentials = localStorage.getItem('userCredentials');
        if (storedCredentials) {
            appState.credentials = JSON.parse(storedCredentials);
        } else {
            console.log('No stored credentials found');
            // For testing purposes - would normally prompt user for credentials
            appState.credentials = {
                uid: '<EMAIL>',
                password: 'test'
            };
        }

        // Set credentials in the global API service if available
        if (window.apiService && appState.credentials.uid && appState.credentials.password) {
            console.log('Setting credentials in API service:', appState.credentials.uid);
            window.apiService.setCredentials(appState.credentials.uid, appState.credentials.password);
        }
    } catch (error) {
        console.error('Error loading credentials:', error);
    }
}

// Reset test counters to zero
function resetTestCounters() {
    elements.totalTestsCounter.textContent = '0';
    elements.passedTestsCounter.textContent = '0';
    elements.failedTestsCounter.textContent = '0';
}

// Update test counters from recent runs data
function updateTestCounters() {
    let totalTests = 0;
    let passedTests = 0;
    let failedTests = 0;
    
    // Count tests in the recent runs cache
    appState.recentRunsCache.forEach(test => {
        totalTests++;
        if (test.status === 'passed') passedTests++;
        if (test.status === 'failed') failedTests++;
    });
    
    // Update counter elements
    elements.totalTestsCounter.textContent = totalTests;
    elements.passedTestsCounter.textContent = passedTests;
    elements.failedTestsCounter.textContent = failedTests;
}

// Set up event listeners for test runner controls
function setupEventListeners() {
    // Run test button
    elements.runTestButton.addEventListener('click', handleRunTest);
    
    // Projects dropdown (maps to QA environments)
    elements.projectSelect.addEventListener('change', function() {
        appState.testRunDefaults.envir = this.value;
    });
    
    // Shell host select
    elements.shellHostSelect.addEventListener('change', function() {
        appState.testRunDefaults.shell_host = this.value;
    });
    
    // Set default values
    elements.projectSelect.value = appState.testRunDefaults.envir;
    elements.shellHostSelect.value = appState.testRunDefaults.shell_host;
}

// Handle the Run Test button click
async function handleRunTest() {
    // Get test case ID from input
    const testCaseId = elements.testCaseIdInput.value.trim();
    if (!testCaseId) {
        showError('Please enter a test case ID');
        return;
    }
    
    console.log('Test Case ID (before conversion):', testCaseId);
    console.log('Test Case ID type:', typeof testCaseId);
    
    // Get selected environment and shell host
    const environment = appState.testRunDefaults.envir;
    const shellHost = appState.testRunDefaults.shell_host;
    
    try {
        // Disable button during API call
        elements.runTestButton.disabled = true;
        elements.runTestButton.textContent = 'Running...';
        
        // Call the API to run the test case
        const result = await runTestCase(testCaseId, environment, shellHost);
        
        if (result && result.tsn_id) {
            showSuccess(`Test case ${testCaseId} started. TSN ID: ${result.tsn_id}`);
            
            // Add to active tests
            addActiveTest(result);
            
            // Clear the input field for next test
            elements.testCaseIdInput.value = '';
        } else {
            showError(`Failed to start test case ${testCaseId}`);
        }
    } catch (error) {
        console.error('Error running test case:', error);
        showError(`Error running test case: ${error.message || 'Unknown error'}`);
    } finally {
        // Re-enable button
        elements.runTestButton.disabled = false;
        elements.runTestButton.textContent = 'Run Test';
    }
}

// Run a test case via the API
async function runTestCase(tcId, environment, shellHost) {
    try {
        console.log('runTestCase received tcId:', tcId, 'type:', typeof tcId);
        
        // Ensure tcId is a string or number, not an object
        if (typeof tcId === 'object') {
            console.error('tcId is an object! Converting to string:', tcId);
            tcId = String(tcId);
        }
        
        const apiService = new UnifiedApiService();
        
        // Set up the parameters for the API call (without tc_id which needs to be passed separately)
        const params = {
            uid: appState.credentials.uid,
            password: appState.credentials.password,
            envir: environment,
            shell_host: shellHost
        };
        
        console.log('Running test case with ID:', tcId, 'and params:', params);
        
        // Call the API with tcId as first parameter and params as second parameter
        const response = await apiService.runTestCase(tcId, params);
        return response;
    } catch (error) {
        console.error('API error running test case:', error);
        throw error;
    }
}

// Start optimized coordinated polling
function startPolling() {
    console.log('Starting optimized coordinated polling...');

    // Clear any existing interval (legacy)
    if (appState.pollInterval) {
        console.log('Clearing existing poll interval');
        clearInterval(appState.pollInterval);
        appState.pollInterval = null;
    }

    // Subscribe to recent runs updates (50 single test cases only)
    window.pollingCoordinator.subscribe('recentRuns', (recentRuns) => {
        console.log(`Received ${recentRuns.length} recent runs update`);
        appState.recentRunsCache = recentRuns;
        processRecentRunsData(recentRuns);
        renderRecentRuns(recentRuns);
        updateTestCounters();

        // Preload test details for visible runs
        window.requestManager.preloadTestDetails(recentRuns.slice(0, 10));
    }, 'config-recent-runs');

    // Subscribe to active tests updates (fast polling for 15 sec to 5-6 min tests)
    window.pollingCoordinator.subscribe('activeTests', (activeTests) => {
        console.log(`Received ${activeTests.length} active tests update`);
        updateActiveTestsDisplay(activeTests);
    }, 'config-active-tests');

    console.log('✅ Subscribed to coordinated polling system');
}

// Update active tests display with detailed information
function updateActiveTestsDisplay(activeTests) {
    console.log(`Updating active tests display with ${activeTests.length} tests`);

    // Update active tests in app state
    appState.activeTests.clear();

    activeTests.forEach(test => {
        appState.activeTests.set(test.tsn_id, {
            ...test,
            detected_at: Date.now()
        });
    });

    // Render active tests with detailed information (ID, Name, Comment, Environment)
    renderActiveTests(activeTests);

    // Update counters
    updateTestCounters();
}

// Render active tests with enhanced information
function renderActiveTests(activeTests) {
    const activeTestsPanel = elements.activeTestsPanel;
    if (!activeTestsPanel) return;

    // Find or create the active tests container within the panel
    let activeTestsContainer = activeTestsPanel.querySelector('.active-tests-list');
    if (!activeTestsContainer) {
        activeTestsContainer = document.createElement('div');
        activeTestsContainer.className = 'active-tests-list';
        activeTestsPanel.appendChild(activeTestsContainer);
    }

    if (activeTests.length === 0) {
        activeTestsContainer.innerHTML = '<p class="ms-fontColor-neutralSecondary">No active tests</p>';
        return;
    }

    const activeTestsHtml = activeTests.map(test => `
        <div class="active-test-item ms-depth-4" style="margin-bottom: 8px; padding: 12px; border-left: 3px solid #0078d4;">
            <div class="test-header" style="display: flex; justify-content: space-between; align-items: center;">
                <div class="test-info">
                    <strong>ID:</strong> ${test.tc_id} | <strong>TSN:</strong> ${test.tsn_id}
                    <div class="test-name" style="font-weight: 600; color: #323130; margin-top: 4px;">
                        ${test.name || 'Test Case'}
                    </div>
                </div>
                <div class="test-status">
                    <span class="ms-Badge ms-Badge--info">${test.status || 'Running'}</span>
                </div>
            </div>
            <div class="test-details" style="margin-top: 8px; font-size: 12px; color: #605e5c;">
                ${test.comments ? `<div><strong>Comment:</strong> ${test.comments}</div>` : ''}
                <div><strong>Environment:</strong> ${test.environment || test.envir || 'N/A'}</div>
                <div><strong>Started:</strong> ${test.start_time ? new Date(test.start_time).toLocaleTimeString() : 'N/A'}</div>
            </div>
        </div>
    `).join('');

    activeTestsContainer.innerHTML = activeTestsHtml;
}

// Process recent runs data to update active tests
function processRecentRunsData(recentRuns) {
    // Check for completed tests
    appState.activeTests.forEach((activeTest, tsnId) => {
        // Find the test in recent runs
        const recentRunData = recentRuns.find(run => run.tsn_id === tsnId);
        
        if (recentRunData) {
            // Update the active test data
            activeTest.data = { ...activeTest.data, ...recentRunData };
            
            // Check if test is completed
            const isCompleted = recentRunData.status === 'passed' || 
                               recentRunData.status === 'failed' ||
                               recentRunData.status === 'error';
            
            if (isCompleted && recentRunData.end_time && !activeTest.completionTimestamp) {
                activeTest.completionTimestamp = Date.now();

                // Trigger status bar event for test completion
                if (window.testStatusBar) {
                    window.testStatusBar.updateTestStatus(
                        recentRunData.tc_id || recentRunData.testId,
                        recentRunData.status
                    );
                } else {
                    // Fallback: dispatch custom event
                    const eventType = recentRunData.status === 'passed' ? 'test-completed' : 'test-failed';
                    document.dispatchEvent(new CustomEvent(eventType, {
                        detail: {
                            testId: recentRunData.tc_id || recentRunData.testId,
                            tsnId: recentRunData.tsn_id,
                            status: recentRunData.status,
                            data: recentRunData
                        }
                    }));
                }

                // Show notification for completed test
                showTestCompletionNotification(recentRunData);
            }
        }
    });
    
    // Remove completed tests after grace period
    const now = Date.now();
    const testsToRemove = [];
    
    appState.activeTests.forEach((activeTest, tsnId) => {
        if (activeTest.completionTimestamp && 
            (now - activeTest.completionTimestamp) > COMPLETED_TEST_GRACE_PERIOD) {
            testsToRemove.push(tsnId);
        }
    });
    
    testsToRemove.forEach(tsnId => {
        appState.activeTests.delete(tsnId);
    });
    
    // Update active tests panel
    renderActiveTests();
}
// Add a test to the active tests panel
function addActiveTest(testData) {
    // Add to our active tests Map
    appState.activeTests.set(testData.tsn_id, {
        data: testData,
        completionTimestamp: null
    });

    // Trigger status bar event for test started
    if (window.testStatusBar) {
        window.testStatusBar.updateTestStatus(testData.tc_id || testData.testId, 'running');
    } else {
        // Fallback: dispatch custom event
        document.dispatchEvent(new CustomEvent('test-started', {
            detail: {
                testId: testData.tc_id || testData.testId,
                tsnId: testData.tsn_id,
                data: testData
            }
        }));
    }

    // Update UI
    renderActiveTests();
}

// Render all active tests in the panel
function renderActiveTests() {
    // Update the active tests count
    elements.activeTestsCount.textContent = appState.activeTests.size;
    
    // Clear current content
    const activeTestsContent = elements.activeTestsPanel.querySelector('.ms-panel-content');
    activeTestsContent.innerHTML = '';
    
    // If no active tests, show empty message
    if (appState.activeTests.size === 0) {
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'ms-empty-message';
        emptyMessage.textContent = 'No active tests';
        activeTestsContent.appendChild(emptyMessage);
        return;
    }
    
    // Create cards for each active test
    appState.activeTests.forEach((activeTest) => {
        const testData = activeTest.data;
        const card = createTestCard(testData);
        activeTestsContent.appendChild(card);
    });
}

// Create a test card element
function createTestCard(testData) {
    const card = document.createElement('div');
    card.className = 'ms-card';
    card.dataset.tsnId = testData.tsn_id;
    
    // Determine status class
    let statusClass = 'ms-status-running';
    if (testData.status === 'passed') statusClass = 'ms-status-passed';
    if (testData.status === 'failed') statusClass = 'ms-status-failed';
    if (testData.status === 'error') statusClass = 'ms-status-error';
    
    // Calculate progress percentage
    const totalCases = testData.total_cases || 1;
    const passedCases = testData.passed_cases || 0;
    const failedCases = testData.failed_cases || 0;
    const completedCases = passedCases + failedCases;
    const progressPercent = Math.floor((completedCases / totalCases) * 100);
    
    card.innerHTML = `
        <div class="ms-card-header ${statusClass}">
            <div class="ms-card-title">Test Case: ${testData.tc_id}</div>
            <div class="ms-card-subtitle">TSN ID: ${testData.tsn_id}</div>
            <div class="ms-card-status">${testData.status || 'Running'}</div>
        </div>
        <div class="ms-card-body">
            <div class="ms-progress">
                <div class="ms-progress-bar" style="width: ${progressPercent}%"></div>
            </div>
            <div class="ms-progress-text">
                ${passedCases} / ${totalCases} Passed, ${failedCases} Failed
            </div>
        </div>
        <div class="ms-card-footer">
            <button class="ms-button ms-button-small ms-button-danger stop-test" data-tsn-id="${testData.tsn_id}">Stop Test</button>
        </div>
    `;
    
    // Add event listener for Stop Test button
    const stopButton = card.querySelector('.stop-test');
    stopButton.addEventListener('click', function() {
        stopTest(this.dataset.tsnId);
    });
    
    return card;
}

// Render recent runs in the table
function renderRecentRuns(recentRuns) {
    console.log('renderRecentRuns called with data:', recentRuns);
    console.log('recentRunsBody element:', elements.recentRunsBody);

    if (!elements.recentRunsBody) {
        console.error('recentRunsBody element not found!');
        return;
    }

    // Clear existing rows
    elements.recentRunsBody.innerHTML = '';

    if (!recentRuns || recentRuns.length === 0) {
        console.log('No recent runs to display');
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="3" style="text-align: center; color: #666;">No recent runs found</td>';
        elements.recentRunsBody.appendChild(row);
        return;
    }

    console.log(`Rendering ${recentRuns.length} recent runs`);

    // Add each run as a row
    recentRuns.forEach(run => {
        const row = document.createElement('tr');
        
        // Format timestamp
        const timestamp = run.start_time ? new Date(run.start_time).toLocaleString() : 'N/A';
        
        // Determine status class
        let statusClass = '';
        if (run.status === 'passed') statusClass = 'ms-text-success';
        if (run.status === 'failed') statusClass = 'ms-text-danger';
        if (run.status === 'error') statusClass = 'ms-text-danger';
        
        row.innerHTML = `
            <td>${run.tc_id}</td>
            <td>${run.tsn_id}</td>
            <td class="${statusClass}">${run.status || 'Running'}</td>
            <td>${timestamp}</td>
            <td>
                <button class="ms-Button ms-Button--primary view-details-btn" 
                        data-tsn-id="${run.tsn_id || run.id}" 
                        title="View detailed information about this test run">
                    <span class="ms-Button-label">View Details</span>
                </button>
            </td>
        `;
        
        // Add event listener for View Details button
        const viewDetailsButton = row.querySelector('.view-details-btn');
        viewDetailsButton.addEventListener('click', function() {
            viewTestDetails(this.dataset.tsnId);
        });
        
        elements.recentRunsBody.appendChild(row);
    });

    // After all rows are added to the table, attach event listeners
    attachViewDetailsListeners();
}

// Stop a running test
async function stopTest(tsnId) {
    try {
        const apiService = new UnifiedApiService();
        await apiService.stopTest(tsnId);
        showSuccess(`Stopping test ${tsnId}...`);
    } catch (error) {
        console.error('Error stopping test:', error);
        showError(`Failed to stop test: ${error.message || 'Unknown error'}`);
    }
}

// View test details with optimized loading
async function viewTestDetails(tsnId) {
    console.log(`Loading test details for TSN ID: ${tsnId}`);

    try {
        // Show loading modal immediately
        showTestDetailsModal(tsnId, null, true);

        // Use request manager for optimized loading (with caching)
        const requestKey = `testDetails_${tsnId}`;
        const testDetails = await window.requestManager.executeRequest(
            requestKey,
            () => window.apiService.getTestDetails(tsnId),
            'testDetails'
        );

        // Update modal with loaded details
        showTestDetailsModal(tsnId, testDetails, false);

    } catch (error) {
        console.error(`Error loading test details for ${tsnId}:`, error);
        showError(`Failed to load test details: ${error.message}`);
    }
}

// Show test details modal with loading state support
function showTestDetailsModal(tsnId, testDetails, isLoading) {
    // Create or get modal
    let modal = document.getElementById('test-details-modal');
    if (!modal) {
        modal = createTestDetailsModal();
        document.body.appendChild(modal);
    }

    const modalContent = modal.querySelector('.modal-content');

    if (isLoading) {
        modalContent.innerHTML = `
            <div class="modal-header">
                <h3>Test Details - TSN ${tsnId}</h3>
                <button class="close-btn" onclick="closeTestDetailsModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="ms-Spinner ms-Spinner--large" style="margin: 40px auto; display: block;"></div>
                <p style="text-align: center; margin-top: 20px;">Loading test details...</p>
            </div>
        `;
    } else if (testDetails) {
        modalContent.innerHTML = generateTestDetailsHTML(tsnId, testDetails);
    }

    modal.style.display = 'block';
    modal.classList.add('show');
}

// Create test details modal structure
function createTestDetailsModal() {
    const modal = document.createElement('div');
    modal.id = 'test-details-modal';
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content" style="max-width: 800px; max-height: 80vh; overflow-y: auto;">
            <!-- Content will be populated dynamically -->
        </div>
    `;

    // Close modal when clicking outside
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeTestDetailsModal();
        }
    });

    return modal;
}

// Generate HTML for test details
function generateTestDetailsHTML(tsnId, testDetails) {
    return `
        <div class="modal-header">
            <h3>Test Details - TSN ${tsnId}</h3>
            <button class="close-btn" onclick="closeTestDetailsModal()">&times;</button>
        </div>
        <div class="modal-body">
            <div class="test-details-grid">
                <div class="detail-item">
                    <strong>Test Case ID:</strong> ${testDetails.tc_id || 'N/A'}
                </div>
                <div class="detail-item">
                    <strong>Name:</strong> ${testDetails.name || 'N/A'}
                </div>
                <div class="detail-item">
                    <strong>Status:</strong>
                    <span class="status-badge status-${(testDetails.status || '').toLowerCase()}">${testDetails.status || 'N/A'}</span>
                </div>
                <div class="detail-item">
                    <strong>Environment:</strong> ${testDetails.environment || testDetails.envir || 'N/A'}
                </div>
                <div class="detail-item">
                    <strong>Start Time:</strong> ${testDetails.start_time ? new Date(testDetails.start_time).toLocaleString() : 'N/A'}
                </div>
                <div class="detail-item">
                    <strong>End Time:</strong> ${testDetails.end_time ? new Date(testDetails.end_time).toLocaleString() : 'Running...'}
                </div>
                ${testDetails.comments ? `<div class="detail-item full-width"><strong>Comments:</strong> ${testDetails.comments}</div>` : ''}
                ${testDetails.details_url ? `<div class="detail-item full-width"><a href="${testDetails.details_url}" target="_blank" class="ms-Button ms-Button--primary">View Full Report</a></div>` : ''}
            </div>
        </div>
    `;
}

// Close test details modal
function closeTestDetailsModal() {
    const modal = document.getElementById('test-details-modal');
    if (modal) {
        modal.style.display = 'none';
        modal.classList.remove('show');
    }
}

// Show test completion notification
function showTestCompletionNotification(testData) {
    const status = testData.status || 'completed';
    const passedCount = testData.passed_cases || 0;
    const failedCount = testData.failed_cases || 0;
    
    const message = `Test ${testData.tc_id} ${status}. ` + 
                   `Cases Passed: ${passedCount}, Cases Failed: ${failedCount}`;
    
    if (status === 'passed') {
        showSuccess(message);
    } else {
        showError(message);
    }
}

// Show success message
function showSuccess(message) {
    const toast = document.createElement('div');
    toast.className = 'ms-toast ms-toast-success';
    toast.innerHTML = `
        <div class="ms-toast-icon">✓</div>
        <div class="ms-toast-message">${message}</div>
    `;
    
    document.body.appendChild(toast);
    
    // Show the toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);
    
    // Hide and remove the toast after 3 seconds
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// Show error message
function showError(message) {
    const toast = document.createElement('div');
    toast.className = 'ms-toast ms-toast-error';
    toast.innerHTML = `
        <div class="ms-toast-icon">!</div>
        <div class="ms-toast-message">${message}</div>
    `;
    
    document.body.appendChild(toast);
    
    // Show the toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);
    
    // Hide and remove the toast after 3 seconds
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// Add this function to handle View Details button clicks
function attachViewDetailsListeners() {
    const viewDetailsButtons = document.querySelectorAll('.view-details-btn');
    viewDetailsButtons.forEach(button => {
        // Remove existing listeners to prevent duplicates
        button.removeEventListener('click', handleViewDetailsClick);
        button.addEventListener('click', handleViewDetailsClick);
    });
}

function handleViewDetailsClick(event) {
    const tsnId = event.target.getAttribute('data-tsn-id') || 
                  event.target.closest('button').getAttribute('data-tsn-id');
    
    console.log('View Details clicked for TSN ID:', tsnId);
    
    if (!tsnId) {
        console.error('No TSN ID found for View Details button');
        showError('Unable to load test details: No test ID found');
        return;
    }

    if (window.testDetailsModal) {
        window.testDetailsModal.show(tsnId);
    } else {
        console.error('Test details modal not available');
        showError('Test details functionality not available');
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initCustomTestRunner);

