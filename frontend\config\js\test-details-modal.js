/**
 * Test Details Modal for Config Page
 * Reuses existing API functions from reports page
 */

class TestDetailsModal {
    constructor() {
        this.modal = null;
        this.init();
        this.setupDependencies();
    }

    setupDependencies() {
        // Create currentState if it doesn't exist (needed by reports functions)
        if (typeof window.currentState === 'undefined') {
            window.currentState = {
                currentTestDetails: null,
                reports: []
            };
        }

        // Create config if it doesn't exist (needed by reports functions)
        if (typeof window.config === 'undefined') {
            window.config = {
                testDetailsEndpoint: '/local/test-details',
                reportingEndpoint: '/local/recent-runs'
            };
        }

        // Add missing parsing functions if they don't exist
        if (typeof window.parseReportSummaryHtml === 'undefined') {
            window.parseReportSummaryHtml = this.parseReportSummaryHtml.bind(this);
        }
        if (typeof window.parseReportDetailsHtml === 'undefined') {
            window.parseReportDetailsHtml = this.parseReportDetailsHtml.bind(this);
        }
        if (typeof window.calculateDuration === 'undefined') {
            window.calculateDuration = this.calculateDuration.bind(this);
        }
    }

    // Simplified HTML parsing functions
    parseReportSummaryHtml(html, testId) {
        console.log('Parsing report summary HTML...');
        
        // Basic parsing - extract what we can from HTML
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        
        // Try to extract basic information
        const summaryData = {
            suite_id: testId,
            suite_name: this.extractTextContent(doc, 'Test Name', 'Unknown Test'),
            status: this.extractStatus(html),
            start_time: this.extractTextContent(doc, 'Start Time'),
            end_time: this.extractTextContent(doc, 'End Time'),
            environment: this.extractTextContent(doc, 'Environment', 'Unknown'),
            owner: this.extractTextContent(doc, 'Owner'),
            passed_cases: this.extractNumber(html, /passed[:\s]*(\d+)/i),
            failed_cases: this.extractNumber(html, /failed[:\s]*(\d+)/i),
            total_cases: this.extractNumber(html, /total[:\s]*(\d+)/i),
            variables: this.extractVariables(html)
        };

        console.log('Parsed summary data:', summaryData);
        return summaryData;
    }

    parseReportDetailsHtml(html, testId) {
        console.log('Parsing report details HTML...');
        
        const detailsData = {
            tsn_id: testId,
            test_cases: [],
            report_html: html
        };

        console.log('Parsed details data:', detailsData);
        return detailsData;
    }

    // Helper functions for HTML parsing
    extractTextContent(doc, label, defaultValue = 'N/A') {
        try {
            const elements = doc.querySelectorAll('td, th, div, span');
            for (let el of elements) {
                if (el.textContent.toLowerCase().includes(label.toLowerCase())) {
                    const next = el.nextElementSibling;
                    if (next) return next.textContent.trim();
                }
            }
        } catch (e) {
            console.warn('Error extracting text content:', e);
        }
        return defaultValue;
    }

    extractStatus(html) {
        const statusMatch = html.match(/(passed|failed|error|running|success)/i);
        return statusMatch ? statusMatch[1].toLowerCase() : 'unknown';
    }

    extractNumber(html, regex) {
        const match = html.match(regex);
        return match ? parseInt(match[1], 10) : 0;
    }

    extractVariables(html) {
        // Try to extract environment variables or parameters
        const variables = {};
        const varMatches = html.match(/(\w+)\s*[=:]\s*([^<\n]+)/g);
        if (varMatches) {
            varMatches.forEach(match => {
                const [, key, value] = match.match(/(\w+)\s*[=:]\s*(.+)/);
                if (key && value) {
                    variables[key.trim()] = value.trim();
                }
            });
        }
        return variables;
    }

    init() {
        // Create modal HTML
        const modalHtml = `
            <div id="test-details-modal" class="test-details-modal">
                <div class="test-details-content">
                    <div class="test-details-header">
                        <h3 id="test-details-title">Test Details</h3>
                        <button class="close-btn" id="close-test-details" title="Close">×</button>
                    </div>
                    <div class="test-details-body" id="test-details-body">
                        <!-- Content will be populated here -->
                    </div>
                </div>
            </div>
        `;

        // Add to DOM
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        this.modal = document.getElementById('test-details-modal');

        // Event listeners
        document.getElementById('close-test-details').addEventListener('click', () => this.hide());
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) this.hide();
        });

        // ESC key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modal.classList.contains('show')) {
                this.hide();
            }
        });
    }

    async show(tsnId) {
        try {
            console.log(`Opening test details modal for TSN ID: ${tsnId}`);
            
            // Show loading state
            this.showLoading();
            this.modal.classList.add('show');

            // Get credentials
            const credentials = this.getCredentials();
            if (!credentials.uid || !credentials.password) {
                throw new Error('No credentials available. Please log in first.');
            }

            let testDetails = null;

            // Try database API first (simpler and more reliable for config page)
            try {
                console.log('Attempting to load test details from database API...');
                if (typeof loadTestDetailsFromDatabaseApi === 'function') {
                    testDetails = await loadTestDetailsFromDatabaseApi(tsnId, credentials);
                    console.log('Successfully loaded from database API:', testDetails);
                }
            } catch (dbError) {
                console.log('Database API failed, trying external API:', dbError.message);
                try {
                    if (typeof loadTestDetailsFromExternalApi === 'function') {
                        testDetails = await loadTestDetailsFromExternalApi(tsnId, credentials);
                        console.log('Successfully loaded from external API:', testDetails);
                    }
                } catch (error) {
                    console.error('External API also failed:', error.message);
                    throw new Error(`Failed to load test details: ${error.message}`);
                }
            }

            if (testDetails) {
                this.renderDetails(testDetails);
            } else {
                throw new Error('No test details returned from API');
            }

        } catch (error) {
            console.error('Error loading test details:', error);
            this.showError('Error loading test details: ' + error.message);
        }
    }

    hide() {
        this.modal.classList.remove('show');
    }

    showLoading() {
        document.getElementById('test-details-title').textContent = 'Loading Test Details...';
        document.getElementById('test-details-body').innerHTML = `
            <div class="loading-spinner">
                <p>Loading test details...</p>
            </div>
        `;
    }

    showError(message) {
        document.getElementById('test-details-title').textContent = 'Error';
        document.getElementById('test-details-body').innerHTML = `
            <div class="error-message">
                <p>${message}</p>
            </div>
        `;
    }

    renderDetails(details) {
        console.log('Rendering test details:', details);
        
        document.getElementById('test-details-title').textContent = `Test Details - ${details.tsn_id}`;
        
        const statusClass = this.getStatusClass(details.status);
        const duration = details.duration || this.calculateDuration(details.start_time, details.end_time);
        
        const html = `
            <div class="test-info-grid">
                <div class="test-info-item">
                    <div class="test-info-label">Test Name</div>
                    <div class="test-info-value">${details.test_name || details.name || 'Unknown Test'}</div>
                </div>
                <div class="test-info-item">
                    <div class="test-info-label">Status</div>
                    <div class="test-info-value">
                        <span class="status-badge ${statusClass}">${details.status || 'Unknown'}</span>
                    </div>
                </div>
                <div class="test-info-item">
                    <div class="test-info-label">Duration</div>
                    <div class="test-info-value">${duration}</div>
                </div>
                <div class="test-info-item">
                    <div class="test-info-label">Start Time</div>
                    <div class="test-info-value">${this.formatDateTime(details.start_time)}</div>
                </div>
                <div class="test-info-item">
                    <div class="test-info-label">Passed Cases</div>
                    <div class="test-info-value" style="color: #107c10; font-weight: 600;">${details.passed_cases || 0}</div>
                </div>
                <div class="test-info-item">
                    <div class="test-info-label">Failed Cases</div>
                    <div class="test-info-value" style="color: #e81123; font-weight: 600;">${details.failed_cases || 0}</div>
                </div>
                <div class="test-info-item">
                    <div class="test-info-label">Environment</div>
                    <div class="test-info-value">${details.environment || 'N/A'}</div>
                </div>
                <div class="test-info-item">
                    <div class="test-info-label">Session ID</div>
                    <div class="test-info-value">${details.tsn_id}</div>
                </div>
            </div>
            
            ${this.renderParameters(details.originalParameters)}
        `;

        document.getElementById('test-details-body').innerHTML = html;
    }

    renderParameters(parameters) {
        if (!parameters || Object.keys(parameters).length === 0) {
            return `
                <div class="parameters-section">
                    <h4>Test Parameters</h4>
                    <p style="color: #666; font-style: italic;">No parameters available</p>
                </div>
            `;
        }

        const paramHtml = Object.entries(parameters)
            .map(([key, value]) => `
                <div class="parameter-item">
                    <span class="parameter-key">${key}:</span>
                    <span class="parameter-value">${value}</span>
                </div>
            `).join('');

        return `
            <div class="parameters-section">
                <h4>Test Parameters</h4>
                <div class="parameters-grid">
                    ${paramHtml}
                </div>
            </div>
        `;
    }

    getStatusClass(status) {
        if (!status) return 'status-unknown';
        const s = status.toLowerCase();
        if (s === 'success' || s === 'passed') return 'status-success';
        if (s === 'failed' || s === 'error') return 'status-failed';
        if (s === 'running') return 'status-running';
        if (s === 'queued') return 'status-queued';
        return 'status-unknown';
    }

    formatDateTime(dateStr) {
        if (!dateStr) return 'N/A';
        try {
            return new Date(dateStr).toLocaleString();
        } catch {
            return dateStr;
        }
    }

    calculateDuration(startTime, endTime) {
        if (!startTime || !endTime) return 'N/A';
        try {
            const start = new Date(startTime);
            const end = new Date(endTime);
            const diffMs = end - start;
            const diffSec = Math.floor(diffMs / 1000);
            const minutes = Math.floor(diffSec / 60);
            const seconds = diffSec % 60;
            return `${minutes}:${seconds.toString().padStart(2, '0')}`;
        } catch {
            return 'N/A';
        }
    }

    getCredentials() {
        // Try multiple sources for credentials
        return {
            uid: sessionStorage.getItem('smarttest_uid') || 
                 (window.appState && window.appState.credentials && window.appState.credentials.uid) || 
                 '',
            password: sessionStorage.getItem('smarttest_pwd') || 
                     (window.appState && window.appState.credentials && window.appState.credentials.password) || 
                     ''
        };
    }
}

// Initialize modal when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.testDetailsModal = new TestDetailsModal();
});

// Also initialize if DOM is already loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.testDetailsModal = new TestDetailsModal();
    });
} else {
    window.testDetailsModal = new TestDetailsModal();
}
