/**
 * Test Details Modal
 * Handles the display of detailed test information in a modal dialog
 */

class TestDetailsModal {
    constructor() {
        this.modal = null;
        this.isVisible = false;
        this.currentTsnId = null;
        
        this.init();
    }
    
    init() {
        // Create modal structure
        this.createModal();
        
        // Add event listeners
        this.addEventListeners();
        
        console.log('Test Details Modal initialized');
    }
    
    createModal() {
        // Create modal overlay
        this.modal = document.createElement('div');
        this.modal.id = 'test-details-modal';
        this.modal.className = 'modal';
        
        // Create modal content
        const modalContent = document.createElement('div');
        modalContent.className = 'modal-content';
        
        // Add modal content structure
        modalContent.innerHTML = `
            <div class="modal-header">
                <h3 id="modal-title">Test Details</h3>
                <button class="close-btn" type="button" aria-label="Close modal">&times;</button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Content will be populated dynamically -->
            </div>
        `;
        
        this.modal.appendChild(modalContent);
        document.body.appendChild(this.modal);
    }
    
    addEventListeners() {
        // Close button
        const closeBtn = this.modal.querySelector('.close-btn');
        closeBtn.addEventListener('click', () => this.hide());
        
        // Click outside to close
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.hide();
            }
        });
        
        // Escape key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isVisible) {
                this.hide();
            }
        });
    }
    
    async show(tsnId) {
        this.currentTsnId = tsnId;
        
        // Update title
        const title = this.modal.querySelector('#modal-title');
        title.textContent = `Test Details - TSN ${tsnId}`;
        
        // Show loading state
        this.showLoading();
        
        // Show modal
        this.modal.style.display = 'block';
        this.modal.classList.add('show');
        this.isVisible = true;
        
        // Load test details
        try {
            const testDetails = await this.loadTestDetails(tsnId);
            this.showTestDetails(testDetails);
        } catch (error) {
            console.error('Error loading test details:', error);
            this.showError('Failed to load test details');
        }
    }
    
    hide() {
        this.modal.style.display = 'none';
        this.modal.classList.remove('show');
        this.isVisible = false;
        this.currentTsnId = null;
    }
    
    showLoading() {
        const modalBody = this.modal.querySelector('#modal-body');
        modalBody.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <div class="loading-spinner"></div>
                <p class="loading-text">Loading test details...</p>
            </div>
        `;
    }
    
    showError(message) {
        const modalBody = this.modal.querySelector('#modal-body');
        modalBody.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <p style="color: #d13438; font-size: 16px; margin: 0;">${message}</p>
            </div>
        `;
    }
    
    async loadTestDetails(tsnId) {
        // Try to use optimized request manager if available
        if (window.simpleRequestManager && typeof window.simpleRequestManager.executeRequest === 'function') {
            const requestKey = `testDetails_${tsnId}`;
            return await window.simpleRequestManager.executeRequest(
                requestKey,
                () => window.apiService.getTestDetails(tsnId),
                'testDetails'
            );
        } else if (window.requestManager && typeof window.requestManager.executeRequest === 'function') {
            const requestKey = `testDetails_${tsnId}`;
            return await window.requestManager.executeRequest(
                requestKey,
                () => window.apiService.getTestDetails(tsnId),
                'testDetails'
            );
        } else {
            // Fallback to direct API call
            return await window.apiService.getTestDetails(tsnId);
        }
    }
    
    showTestDetails(testDetails) {
        const modalBody = this.modal.querySelector('#modal-body');
        
        modalBody.innerHTML = `
            <div class="test-details-grid">
                <div class="detail-item">
                    <strong>Test Case ID:</strong>
                    <span>${testDetails.tc_id || 'N/A'}</span>
                </div>
                <div class="detail-item">
                    <strong>Name:</strong>
                    <span>${testDetails.name || 'N/A'}</span>
                </div>
                <div class="detail-item">
                    <strong>Status:</strong>
                    <span class="status-badge status-${(testDetails.status || '').toLowerCase()}">${testDetails.status || 'N/A'}</span>
                </div>
                <div class="detail-item">
                    <strong>Environment:</strong>
                    <span>${testDetails.environment || testDetails.envir || 'N/A'}</span>
                </div>
                <div class="detail-item">
                    <strong>Start Time:</strong>
                    <span>${testDetails.start_time ? new Date(testDetails.start_time).toLocaleString() : 'N/A'}</span>
                </div>
                <div class="detail-item">
                    <strong>End Time:</strong>
                    <span>${testDetails.end_time ? new Date(testDetails.end_time).toLocaleString() : 'Running...'}</span>
                </div>
                ${testDetails.comments ? `
                    <div class="detail-item full-width">
                        <strong>Comments:</strong>
                        <span>${testDetails.comments}</span>
                    </div>
                ` : ''}
                ${testDetails.details_url ? `
                    <div class="detail-item full-width">
                        <strong>Full Report:</strong>
                        <a href="${testDetails.details_url}" target="_blank" class="ms-Button ms-Button--primary">
                            View Full Report
                        </a>
                    </div>
                ` : ''}
            </div>
        `;
    }
    
    // Public method to check if modal is visible
    isOpen() {
        return this.isVisible;
    }
    
    // Public method to get current TSN ID
    getCurrentTsnId() {
        return this.currentTsnId;
    }
}

// Initialize the modal when the script loads
document.addEventListener('DOMContentLoaded', () => {
    window.testDetailsModal = new TestDetailsModal();
});

// Also initialize immediately if DOM is already loaded
if (document.readyState === 'loading') {
    // DOM is still loading, wait for DOMContentLoaded
} else {
    // DOM is already loaded
    window.testDetailsModal = new TestDetailsModal();
}
