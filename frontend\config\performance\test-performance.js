/**
 * Performance Optimization Test Suite
 * Verifies that all performance optimizations are working correctly
 */

class PerformanceTest {
    constructor() {
        this.results = [];
        this.startTime = Date.now();
    }

    async runAllTests() {
        console.log('🧪 Starting performance optimization tests...');
        
        try {
            await this.testRequestManager();
            await this.testPollingCoordinator();
            await this.testCaching();
            await this.testApiOptimizations();
            await this.testPerformanceMonitor();
            
            this.displayResults();
        } catch (error) {
            console.error('❌ Performance tests failed:', error);
        }
    }

    async testRequestManager() {
        console.log('Testing Request Manager...');
        
        // Test 1: Request Manager exists
        this.assert(!!window.requestManager, 'Request Manager initialized');
        
        // Test 2: Request deduplication
        const startTime = Date.now();
        const promises = [
            window.requestManager.executeRequest('test1', () => this.mockApiCall(100), 'recentRuns'),
            window.requestManager.executeRequest('test1', () => this.mockApiCall(100), 'recentRuns'),
            window.requestManager.executeRequest('test1', () => this.mockApiCall(100), 'recentRuns')
        ];
        
        const results = await Promise.all(promises);
        const endTime = Date.now();
        
        this.assert(results.every(r => r === results[0]), 'Request deduplication works');
        this.assert(endTime - startTime < 200, 'Deduplication improves performance');
        
        // Test 3: Caching
        const cachedStart = Date.now();
        const cachedResult = await window.requestManager.executeRequest('test1', () => this.mockApiCall(100), 'recentRuns');
        const cachedEnd = Date.now();
        
        this.assert(cachedEnd - cachedStart < 50, 'Caching provides fast responses');
        this.assert(cachedResult === results[0], 'Cached result matches original');
    }

    async testPollingCoordinator() {
        console.log('Testing Polling Coordinator...');
        
        // Test 1: Polling Coordinator exists
        this.assert(!!window.pollingCoordinator, 'Polling Coordinator initialized');
        
        // Test 2: Subscription system
        let receivedData = null;
        const testCallback = (data) => { receivedData = data; };
        
        window.pollingCoordinator.subscribe('recentRuns', testCallback, 'test-component');
        
        // Simulate data update
        window.pollingCoordinator.updateDataStore('recentRuns', [{ test: 'data' }]);
        window.pollingCoordinator.notifySubscribers('recentRuns', [{ test: 'data' }]);
        
        this.assert(receivedData !== null, 'Subscription system works');
        this.assert(receivedData[0].test === 'data', 'Data passed correctly to subscribers');
        
        // Cleanup
        window.pollingCoordinator.unsubscribe('test-component');
    }

    async testCaching() {
        console.log('Testing Caching System...');
        
        // Test 1: Cache hit/miss tracking
        const initialMetrics = window.requestManager.getMetrics();
        
        // Make a new request (cache miss)
        await window.requestManager.executeRequest('cache-test-1', () => this.mockApiCall(50), 'recentRuns');
        
        // Make the same request again (cache hit)
        await window.requestManager.executeRequest('cache-test-1', () => this.mockApiCall(50), 'recentRuns');
        
        const finalMetrics = window.requestManager.getMetrics();
        
        this.assert(finalMetrics.cacheHits > initialMetrics.cacheHits, 'Cache hits are tracked');
        this.assert(finalMetrics.cacheHitRatio > 0, 'Cache hit ratio is calculated');
    }

    async testApiOptimizations() {
        console.log('Testing API Optimizations...');
        
        // Test 1: API service exists and is optimized
        this.assert(!!window.apiService, 'API Service exists');
        
        if (window.apiService) {
            // Check if API methods are wrapped with request manager
            const getRecentRunsStr = window.apiService.getRecentRuns.toString();
            this.assert(
                getRecentRunsStr.includes('requestManager') || getRecentRunsStr.includes('executeRequest'),
                'API methods are optimized with request manager'
            );
        }
    }

    async testPerformanceMonitor() {
        console.log('Testing Performance Monitor...');
        
        // Test 1: Performance Monitor exists
        this.assert(!!window.performanceMonitor, 'Performance Monitor initialized');
        
        // Test 2: Metrics collection
        const summary = window.performanceMonitor.getSummary();
        this.assert(typeof summary === 'object', 'Performance summary is available');
        this.assert(typeof summary.cacheEfficiency === 'object', 'Cache efficiency metrics available');
        this.assert(typeof summary.responseTime === 'object', 'Response time metrics available');
        
        // Test 3: Event logging
        window.performanceMonitor.logEvent('test_event', { test: true });
        const events = JSON.parse(sessionStorage.getItem('perf_events') || '[]');
        this.assert(events.some(e => e.type === 'test_event'), 'Event logging works');
    }

    async mockApiCall(delay = 100) {
        return new Promise(resolve => {
            setTimeout(() => {
                resolve({ mock: 'data', timestamp: Date.now() });
            }, delay);
        });
    }

    assert(condition, message) {
        const result = {
            test: message,
            passed: !!condition,
            timestamp: Date.now()
        };
        
        this.results.push(result);
        
        if (condition) {
            console.log(`✅ ${message}`);
        } else {
            console.error(`❌ ${message}`);
        }
    }

    displayResults() {
        const totalTests = this.results.length;
        const passedTests = this.results.filter(r => r.passed).length;
        const failedTests = totalTests - passedTests;
        const duration = Date.now() - this.startTime;
        
        console.log('\n📊 Performance Test Results:');
        console.log(`Total Tests: ${totalTests}`);
        console.log(`Passed: ${passedTests}`);
        console.log(`Failed: ${failedTests}`);
        console.log(`Duration: ${duration}ms`);
        console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
        
        if (failedTests > 0) {
            console.log('\n❌ Failed Tests:');
            this.results.filter(r => !r.passed).forEach(result => {
                console.log(`  - ${result.test}`);
            });
        }
        
        // Store results for analysis
        sessionStorage.setItem('performance_test_results', JSON.stringify({
            results: this.results,
            summary: {
                total: totalTests,
                passed: passedTests,
                failed: failedTests,
                duration: duration,
                successRate: (passedTests / totalTests) * 100
            },
            timestamp: new Date().toISOString()
        }));
        
        return passedTests === totalTests;
    }

    // Static method to run tests from console
    static async run() {
        const test = new PerformanceTest();
        return await test.runAllTests();
    }
}

// Auto-run tests in development mode
if (window.location.search.includes('test=performance') || window.location.search.includes('debug=true')) {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            PerformanceTest.run();
        }, 3000); // Wait 3 seconds for everything to initialize
    });
}

// Make available globally for manual testing
window.PerformanceTest = PerformanceTest;

export default PerformanceTest;
