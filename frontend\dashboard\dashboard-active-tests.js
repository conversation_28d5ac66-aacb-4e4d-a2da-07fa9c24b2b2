/**
 * dashboard-active-tests.js
 *
 * This module manages the 'Active Tests' panel, providing real-time updates on running tests.
 * It polls for test statuses, updates the UI, and handles the transition of tests to a completed state.
 */

import { config } from './dashboard-config.js';
import * as api from './dashboard-api.js';
import { notifications, incrementCounter } from './dashboard-ui.js';
import { formatters } from './formatters.js';
import { refreshRecentRuns } from './dashboard-recent-runs.js';

let pollingIntervalId = null;

/**
 * Initializes the active tests polling mechanism.
 */
export function initializeActiveTests() {
    if (pollingIntervalId) {
        clearInterval(pollingIntervalId);
    }
    pollingIntervalId = setInterval(pollAndupdateActiveTests, config.constants.ACTIVE_TESTS_POLLING_INTERVAL);
    updateActiveTestsDisplay(); // Initial render
}

/**
 * Polls for recent runs and updates the active tests state and UI.
 */
async function pollAndupdateActiveTests() {
    if (document.hidden) {
        return; // Don't poll if the page is not visible
    }

    try {
        const recentRuns = await api.getRecentRuns(50); // Fetch a sufficient number to find active tests
        const activeTestIds = new Set();

        recentRuns.forEach(run => {
            const { tsn_id, status, start_time, end_time } = run;
            const isActive = !end_time && (status === 'running' || status === 'queued');

            if (isActive) {
                activeTestIds.add(tsn_id);
                if (!config.state.activeTests.has(tsn_id)) {
                    // New active test detected
                    config.state.activeTests.set(tsn_id, { ...run, detected_at: Date.now() });
                }
            }
        });

        // Process completions for tests that were previously active but are no longer
        for (const [tsn_id, testData] of config.state.activeTests.entries()) {
            if (!activeTestIds.has(tsn_id)) {
                handleTestCompletion(tsn_id, testData);
            }
        }

        updateActiveTestsDisplay();
    } catch (error) {
        console.error('Error polling for active tests:', error);
    }
}

/**
 * Handles the logic for when a test is considered complete.
 * @param {number} tsn_id - The ID of the completed test.
 * @param {object} testData - The data of the test from the activeTests map.
 */
async function handleTestCompletion(tsn_id, testData) {
    // Fetch the final, detailed result for the completed test
    const finalResult = await api.getTestDetails(tsn_id).catch(() => null);

    if (finalResult) {
        const { status, passed_cases, failed_cases, total_cases } = finalResult;
        const suiteName = testData.suite_name || `Test Run ${tsn_id}`;

        if (status === 'passed') {
            notifications.success(`Test '${suiteName}' passed.`, `Cases: ${passed_cases}/${total_cases}`);
            incrementCounter('passed');
        } else {
            notifications.error(`Test '${suiteName}' failed.`, `Cases: ${failed_cases} failed, ${passed_cases} passed`);
            incrementCounter('failed');
        }
    }

    // Mark for removal after a grace period to prevent UI flickering
    setTimeout(() => {
        config.state.activeTests.delete(tsn_id);
        updateActiveTestsDisplay();
    }, config.constants.COMPLETED_TEST_REMOVAL_DELAY);

    // Trigger a refresh of the recent runs table
    refreshRecentRuns();
}

/**
 * Updates the 'Active Tests' panel in the UI based on the current state.
 */
function updateActiveTestsDisplay() {
    const container = config.elements.activeTestsContainer;
    if (!container) return;

    const activeTestsArray = Array.from(config.state.activeTests.values());

    if (activeTestsArray.length === 0) {
        container.innerHTML = '<div class="ms-empty-message">No tests are currently running.</div>';
        return;
    }

    // Sort tests by start time, most recent first
    activeTestsArray.sort((a, b) => new Date(b.start_time) - new Date(a.start_time));

    container.innerHTML = activeTestsArray.map(test => createActiveTestCard(test)).join('');

    // Attach event listeners to stop buttons
    container.querySelectorAll('.stop-test-btn').forEach(button => {
        button.addEventListener('click', handleStopTestClick);
    });
}

/**
 * Creates the HTML for a single active test card.
 * @param {object} test - The active test data.
 * @returns {string} The HTML string for the card.
 */
function createActiveTestCard(test) {
    const { tsn_id, suite_name, user, start_time, status } = test;
    const elapsedTime = formatters.formatDuration(new Date(start_time), new Date());

    return `
        <div class="ms-active-test-card" data-tsn-id="${tsn_id}">
            <div class="ms-card-header">
                <h4 class="ms-card-title">${suite_name || `Test Run ${tsn_id}`}</h4>
                <span class="ms-card-status ${formatters.getStatusClass(status)}">${status}</span>
            </div>
            <div class="ms-card-body">
                <p><strong>User:</strong> ${formatters.formatUserEmail(user)}</p>
                <p><strong>Started:</strong> ${formatters.formatDateTime(start_time)}</p>
                <p><strong>Duration:</strong> ${elapsedTime}</p>
            </div>
            <div class="ms-card-footer">
                <button class="ms-Button ms-Button--danger stop-test-btn" data-tsn-id="${tsn_id}">
                    <span class="ms-Button-label">Stop Test</span>
                </button>
            </div>
        </div>
    `;
}

/**
 * Handles the click event for the 'Stop Test' button.
 * @param {Event} event - The click event.
 */
async function handleStopTestClick(event) {
    const button = event.currentTarget;
    const tsn_id = button.dataset.tsnId;

    if (!confirm(`Are you sure you want to stop test run ${tsn_id}?`)) {
        return;
    }

    try {
        button.disabled = true;
        button.querySelector('.ms-Button-label').textContent = 'Stopping...';
        await api.stopTestRun(tsn_id);
        notifications.warning(`Stop request sent for test run ${tsn_id}.`, 'Test Stop');
    } catch (error) {
        console.error(`Error stopping test ${tsn_id}:`, error);
        notifications.error(`Failed to stop test run ${tsn_id}.`, 'API Error');
        button.disabled = false;
        button.querySelector('.ms-Button-label').textContent = 'Stop Test';
    }
}
