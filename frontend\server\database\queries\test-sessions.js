/**
 * Test Session Queries
 * Provides functions for querying test sessions
 */
const QueryBuilder = require('../utils/query-builder');
const formatter = require('../utils/result-formatter');

/**
 * Get active test sessions
 * @param {Object} connection - Database connection
 * @param {Object} filters - Optional filters
 * @returns {Promise<Array>} - Active test sessions
 */
async function getActiveTests(connection, filters = {}) {
  const { uid, limit = 20 } = filters;

  // Create query builder
  const queryBuilder = new QueryBuilder();

  // Base query
  queryBuilder.select('test_session s', [
    's.tsn_id',
    'COALESCE(s.tc_id, 0) as tc_id',
    's.uid as initiator_user',
    's.start_ts as creation_time'
  ]);

  // Filter for active tests (end_ts is NULL)
  queryBuilder.where('s.end_ts', 'IS', null);

  // Filter by user ID if provided
  if (uid) {
    queryBuilder.where('s.uid', '=', uid);
  }

  // Add ordering and limit
  queryBuilder.orderBy('s.start_ts', 'DESC');
  queryBuilder.limit(limit);

  // Build and execute query
  const { sql, params } = queryBuilder.build();

  try {
    const rows = await connection.query(sql, params);
    return formatter.formatActiveTests(rows, uid);
  } catch (error) {
    console.error('Error executing getActiveTests query:', error);

    // Try a simpler fallback query if the main query fails
    try {
      console.log('Attempting fallback query for active tests');
      const fallbackSql = `
        SELECT tsn_id, tc_id, uid, start_ts
        FROM test_session
        WHERE end_ts IS NULL
        ORDER BY start_ts DESC
        LIMIT ?
      `;
      const fallbackRows = await connection.query(fallbackSql, [limit]);
      return formatter.formatActiveTests(fallbackRows, uid);
    } catch (fallbackError) {
      console.error('Fallback query also failed:', fallbackError);
      return [];
    }
  }
}

/**
 * Get recent test sessions
 * @param {Object} connection - Database connection
 * @param {Object} filters - Optional filters
 * @returns {Promise<Object>} - Recent test sessions
 */
async function getRecentRuns(connection, filters = {}) {
  // If a time_range is specified (implying a full load for that range)
  // and no specific limit is given, use a very high default.
  // Otherwise, use the provided limit or a smaller default (e.g., for other uses of getRecentRuns).
  let effectiveLimit;
  if (filters.time_range && filters.limit === undefined) {
    effectiveLimit = 1500; // Default for full time-range loads (effectively "all")
    // console.log(`🔍 Database Query: No explicit limit for time_range, using default full load limit: ${effectiveLimit}`);
  } else if (filters.limit !== undefined) {
    const parsedLimit = parseInt(filters.limit, 10);
    effectiveLimit = parsedLimit === -1 ? 1500 : parsedLimit; // Interpret -1 as "all"
    // console.log(`🔍 Database Query: Explicit limit provided: ${filters.limit}, using: ${effectiveLimit}`);
  } else {
    effectiveLimit = 100; // Default for other cases (e.g., if called without time_range or limit by other services)
    // console.log(`🔍 Database Query: No time_range and no limit, using default: ${effectiveLimit}`);
  }

  try {
   // console.log(`[GET_RECENT_RUNS] Initial filters:`, JSON.stringify(filters));

    let baseSql = `
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid,
             ts.start_ts, ts.end_ts, ts.error,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
    `;

    const whereClauses = [];
    const queryParams = []; // For WHERE clause parameters
    
    // Add since_id filter if provided
    if (filters.since_id && !isNaN(parseInt(filters.since_id))) {
      const sinceId = parseInt(filters.since_id);
      whereClauses.push('ts.tsn_id > ?');
      queryParams.push(sinceId);
      console.log(`[GET_RECENT_RUNS] Adding since_id filter: tsn_id > ${sinceId}`);
    }

    let timeRangeFilterInput = filters.time_range;
    let parsedTimeRange = timeRangeFilterInput;

    if (typeof timeRangeFilterInput === 'string') {
        try {
            if (timeRangeFilterInput.startsWith('{') && timeRangeFilterInput.endsWith('}')) {
                parsedTimeRange = JSON.parse(timeRangeFilterInput);
            }
        } catch (e) {
            console.warn(`[GET_RECENT_RUNS] Time range string was not valid JSON: ${timeRangeFilterInput}`);
            parsedTimeRange = timeRangeFilterInput; // Keep original string if parsing failed
        }
    }
   // console.log(`[GET_RECENT_RUNS] Parsed timeRangeFilter:`, parsedTimeRange);

    if (parsedTimeRange && parsedTimeRange !== 'all') {
        let startDate;
        let endDate;
        const now = new Date();

        if (typeof parsedTimeRange === 'object' && parsedTimeRange.type === 'custom' && parsedTimeRange.start && parsedTimeRange.end) {
            startDate = new Date(parsedTimeRange.start);
            endDate = new Date(parsedTimeRange.end);
            endDate.setHours(23, 59, 59, 999);
           // console.log(`[GET_RECENT_RUNS] Custom range: Start=${startDate.toISOString()}, End=${endDate.toISOString()}`);
        } else if (typeof parsedTimeRange === 'string') {
            switch (parsedTimeRange) {
                case '1h': startDate = new Date(now.getTime() - 1 * 60 * 60 * 1000); break;
                case '24h': startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000); break;
                case '7d': startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000); break;
                case '30d': startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000); break;
                case '3m': startDate = new Date(new Date().setMonth(now.getMonth() - 3)); break;
                default: console.warn(`[GET_RECENT_RUNS] Unknown time range string: ${parsedTimeRange}`);
            }
            if(startDate) console.log(`[GET_RECENT_RUNS] Predefined range '${parsedTimeRange}': Start=${startDate.toISOString()}`);
        }

        if (startDate) {
            const formattedStartDate = startDate.toISOString().slice(0, 19).replace('T', ' ');
            whereClauses.push('ts.start_ts >= ?');
            queryParams.push(formattedStartDate);
        }
        if (endDate) {
            const formattedEndDate = endDate.toISOString().slice(0, 19).replace('T', ' ');
            whereClauses.push('ts.start_ts <= ?');
            queryParams.push(formattedEndDate);
        }
        console.log(`[GET_RECENT_RUNS] WHERE clauses: ${whereClauses.join(' AND ')}, Query params for WHERE: ${JSON.stringify(queryParams)}`);
    } else {
         console.log(`[GET_RECENT_RUNS] No time range filter applied (all or undefined).`);
    }

    if (whereClauses.length > 0) {
        baseSql += ` WHERE ${whereClauses.join(' AND ')}`;
    }

    baseSql += ` ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?`;

    const finalSqlParams = [...queryParams, effectiveLimit];

    console.log(`[GET_RECENT_RUNS] Final SQL: ${baseSql}`);
    console.log(`[GET_RECENT_RUNS] Final Params: ${JSON.stringify(finalSqlParams)}`);

    const rows = await connection.query(baseSql, finalSqlParams);

    console.log(`✅ Database Query: Retrieved ${rows.length} test sessions`);

    // Log the first 3 full rows from the database for debugging
    console.log('📊 First 3 FULL rows from database:');
    for (let i = 0; i < Math.min(3, rows.length); i++) {
      console.log(`Row ${i+1} start_ts: ${rows[i].start_ts}, end_ts: ${rows[i].end_ts}`);
    }

    if (rows.length === 0) {
      console.log('No test sessions returned from database query');
      // Add debugging: Let's check what the most recent sessions look like without time filter
      console.log('🔍 Checking most recent sessions without time filter for debugging:');
      try {
        const debugSql = 'SELECT tsn_id, start_ts, end_ts FROM test_session ORDER BY start_ts DESC LIMIT 5';
        const debugRows = await connection.query(debugSql, []);
        console.log('📊 Most recent 5 sessions in DB:');
        debugRows.forEach((row, idx) => {
          console.log(`  ${idx+1}. tsn_id: ${row.tsn_id}, start_ts: ${row.start_ts}`);
        });
      } catch (debugError) {
        console.log('Debug query failed:', debugError.message);
      }
      return [];
    }

    // IMPORTANT: Detect and correct data format issues
    rows.forEach(row => {
      // Fix NULL values that might be returned as strings
      if (row.start_ts === 'NULL') row.start_ts = null;
      if (row.end_ts === 'NULL') row.end_ts = null;
      if (row.uid === 'NULL') row.uid = null;
      if (row.tc_id === 'NULL') row.tc_id = null;
      if (row.ts_id === 'NULL') row.ts_id = null;

      // Check if dates contain email addresses (data corruption)
      if (row.end_ts && typeof row.end_ts === 'string' && row.end_ts.includes('@')) {
        console.error(`⚠️ DATA ERROR: end_ts contains an email address: ${row.end_ts}`);
        row.end_ts = null; // Reset to null as it's invalid
      }

      if (row.start_ts && typeof row.start_ts === 'string' && row.start_ts.includes('@')) {
        console.error(`⚠️ DATA ERROR: start_ts contains an email address: ${row.start_ts}`);
        row.start_ts = null; // Reset to null as it's invalid
      }

      // Set default environment since we can't get it from the database
      row.environment = 'qa02';
    });

    // Log a sample of the raw database data
    //console.log(`📊 Sample RAW data from database (first row):`);
    //console.log(JSON.stringify({
    //  tsn_id: rows[0].tsn_id,
    //  tc_id: rows[0].tc_id,
    //  uid: rows[0].uid,
    //  start_ts: rows[0].start_ts,
    //  end_ts: rows[0].end_ts,
    //  report_excerpt: rows[0].report ? rows[0].report.substring(0, 100) + '...' : null
    //}, null, 2));

    // Process results in sequential batches to reduce SSH connection load
    const results = [];
    const batchSize = 5;
    const numBatches = Math.ceil(rows.length / batchSize);

    console.log(`⏳ Processing ${rows.length} sessions in ${numBatches} batches...`);

    // Create batches for processing
    for (let batchIndex = 0; batchIndex < numBatches; batchIndex++) {
      const batchStart = batchIndex * batchSize;
      const batchEnd = Math.min(batchStart + batchSize, rows.length);
      const batch = rows.slice(batchStart, batchEnd);

      // Process each session in the current batch
      for (const session of batch) {
        try {
          // Process the session data using the HTML report data
          const processedSession = processSessionData(session);
          results.push(processedSession);
        } catch (error) {
          console.error(`❌ Error processing session ${session.tsn_id}:`, error);

          // Add basic session data even if there was an error
          results.push(createBasicSessionData(session));
        }
      }

      // Add a small delay between batches to prevent SSH channel issues
      if (batchIndex < numBatches - 1) {
        await new Promise(resolve => setTimeout(resolve, 200));
      }
    }

    console.log(`✅ Processed ${results.length} sessions successfully`);

    // Log a sample of the processed data
    //console.log(`📊 Sample PROCESSED data (first row):`);
    //console.log(JSON.stringify(results[0], null, 2));

    const totalRecords = results.length;
    const highestTsnIdInResponse = results.length > 0 ? results[0].tsn_id : null;

    if (filters.since_tsn_id) {
      // This is an incremental fetch, db.getRecentRuns is expected to return newRuns and latestTsnIdInDelta
      // For now, we'll assume all results are new if since_tsn_id is present.
      // A more robust implementation would filter results based on since_tsn_id.
      return {
        newRuns: results,
        totalRecords, // This might represent the total in the new set, or overall total depending on query
        latestTsnIdInDelta: highestTsnIdInResponse // Assuming highestTsnId is the latest in this delta
      };
    } else {
      // This is a full fetch
      return {
        runs: results,
        totalRecords,
        highestTsnIdInResponse
      };
    }
  } catch (error) {
    console.error('Error in getRecentRuns:', error);
    // Return a structure that won't break the consuming service in case of error
    return {
      runs: [],
      newRuns: [],
      totalRecords: 0,
      highestTsnIdInResponse: null,
      latestTsnIdInDelta: null
    };
  }
}

/**
 * Process a session's data without additional database queries
 * @param {Object} session - The session data from database
 * @returns {Object} - Processed session data
 */
function processSessionData(session) {
  try {
    // Extract test name - use the one from the query if available
    let testName = session.test_name;
    let testType = session.type || 'Unknown';
    let passedCases = 0;
    let failedCases = 0;
    let environment = session.environment || 'qa02'; // Default
    let status = 'Unknown';

    if (session.report) {
      // Extract additional data from HTML report if available

      // Extract pass/fail counts
      const passMatch = session.report.match(/Case\(s\) passed: (\d+)/);
      const failMatch = session.report.match(/Case\(s\) failed: (\d+)/);

      if (passMatch && passMatch.length > 1) {
        passedCases = parseInt(passMatch[1], 10);
      }

      if (failMatch && failMatch.length > 1) {
        failedCases = parseInt(failMatch[1], 10);
      }

      // Check for fail/pass span to determine status
      if (session.report.includes("<span style='color:red'>FAIL</span>")) {
        status = 'Failed';
      } else if (session.report.includes("<span style='color:green'>PASS</span>")) {
        status = 'Passed';
      }
    }

    // If no test name was found, use fallbacks
    if (!testName) {
      if (session.tc_id) {
        testName = `Check Test Case ${session.tc_id}`;
      } else if (session.ts_id) {
        testName = `Test Suite ${session.ts_id}`;
      } else {
        testName = `Session ${session.tsn_id}`;
      }
    }

    // Determine test ID to display
    const displayId = session.tc_id || session.ts_id || session.pj_id || session.tsn_id;

    // Calculate duration if start_ts and end_ts are available
    let duration = null;
    if (session && session.start_ts && session.end_ts) {
      try {
        const start = new Date(session.start_ts);
        const end = new Date(session.end_ts);
        if (!isNaN(start.getTime()) && !isNaN(end.getTime())) {
          const durationMs = end - start;
          const minutes = Math.floor(durationMs / 60000);
          const seconds = Math.floor((durationMs % 60000) / 1000);
          duration = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }
      } catch (e) {
        console.error('Error calculating duration:', e);
      }
    }

    // Determine status if not already set from report
    if (status === 'Unknown') {
      if (session.error === 'Quequed') {
        status = 'Queued';
      } else if (session.error === 'Running' || (!session.end_ts && session.start_ts)) {
        status = 'Running';
      } else if (session.end_ts) {
        if (failedCases > 0) {
          status = 'Failed';
        } else if (passedCases > 0) {
          status = 'Passed';
        } else {
          status = 'Completed';
        }
      }
    }

    // Calculate pass rate
    const totalCases = passedCases + failedCases;
    const passRate = totalCases > 0
      ? Math.round((passedCases / totalCases) * 100)
      : 0;

    // Format user display name from email
    let userDisplay = 'Unknown';
    if (session.uid) {
      const userId = session.uid;
      if (typeof userId === 'string' && userId.includes('@')) {
        userDisplay = userId.split('@')[0]; // Extract username from email
      } else if (userId) {
        userDisplay = String(userId);
      }
    }

    return {
      tsn_id: session.tsn_id,
      test_id: displayId,
      tc_id: session.tc_id,
      test_name: testName,
      type: testType,
      envir: environment,
      status: status,
      start_time: session.start_ts,
      end_time: session.end_ts,
      duration: duration,
      total_cases: totalCases,
      passed_cases: passedCases,
      failed_cases: failedCases,
      pass_rate: passRate,
      uid: session.uid,
      user_display: userDisplay  // Add formatted username for display
    };
  } catch (error) {
    console.error('Error in processSessionData:', error);
    return createBasicSessionData(session);
  }
}

/**
 * Create a basic session data object as fallback
 * @param {Object} session - The session data from database
 * @returns {Object} - Basic session data
 */
function createBasicSessionData(session) {
  const testType = session.type || (session.tc_id ? 'Test Case' : (session.ts_id ? 'Test Suite' : 'Project'));
  const displayId = session.tc_id || session.ts_id || session.pj_id || session.tsn_id;

  // Format user display name
  let userDisplay = 'Unknown';
  if (session.uid) {
    const userId = session.uid;
    if (typeof userId === 'string' && userId.includes('@')) {
      userDisplay = userId.split('@')[0];
    } else if (userId) {
      userDisplay = String(userId);
    }
  }

  return {
    tsn_id: session.tsn_id,
    test_id: displayId,
    tc_id: session.tc_id,
    test_name: session.test_name || `No name assigned (${testType} ${displayId})`,
    type: testType,
    envir: session.environment || 'qa02',
    status: session.error || (session.end_ts ? 'Completed' : 'Running'),
    start_time: session.start_ts,
    end_time: session.end_ts,
    duration: null,
    total_cases: 0,
    passed_cases: 0,
    failed_cases: 0,
    pass_rate: 0,
    uid: session.uid,
    user_display: userDisplay  // Add formatted username for display
  };
}

/**
 * Get test session details
 * @param {Object} connection - Database connection
 * @param {string|number} tsn_id - Test session ID
 * @returns {Promise<Object>} - Test session details
 */
async function getTestSessionDetails(connection, tsn_id) {
  // Log detailed connection information to help diagnose issues
  console.log(`[DEBUG] Database connection status:`, {
    connected: connection && connection.connection ? true : false,
    threadId: connection && connection.connection ? connection.connection.threadId : null,
    database: connection && connection.config ? connection.config.database : null,
    connectionId: connection && connection.connection ? connection.connection.connectionId : null
  });
  console.log(`[DEBUG] Fetching test session details for tsn_id: ${tsn_id}`);
  // Add explicit schema prefix to all tables like in the PowerShell script
  const schemaPrefix = 'rgs_test.'; // Add schema prefix matching PowerShell script

  const sessionSql = `
    SELECT
      ts.tsn_id,
      ts.tc_id,
      ts.ts_id,
      ts.pj_id,
      ts.uid,
      ts.start_ts,
      ts.end_ts,
      ts.error,
      ts.report,
      /* Removed u.name as user_name which doesn't exist in the database */
      COALESCE(tc.name, tsui.name) as test_name, /* Added test_name directly from session query */
      CASE
        WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
        WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
        ELSE 'Project'
      END AS type
    FROM ${schemaPrefix}test_session ts
    /* Removed LEFT JOIN user u ON ts.uid = u.uid since the user table doesn't appear to exist */
    LEFT JOIN ${schemaPrefix}test_case tc ON ts.tc_id = tc.tc_id
    LEFT JOIN ${schemaPrefix}test_suite tsui ON ts.ts_id = tsui.ts_id
    WHERE ts.tsn_id = ?
  `;

  const testCasesSql = `
    SELECT
      tr.tc_id,
      tr.seq_index,
      tr.outcome,
      tr.creation_time,
      tr.cnt,
      tc.name as test_case_name,
      tc.comments as description,
      IFNULL(i.txt, '') as input_data,
      IFNULL(o.txt, '') as output_data,
      -- Removed reference to non-existent error table
      CASE WHEN tr.outcome != 'P' THEN tr.outcome ELSE NULL END as error_message
    FROM ${schemaPrefix}test_result tr
    LEFT JOIN ${schemaPrefix}test_case tc ON tr.tc_id = tc.tc_id
    LEFT JOIN ${schemaPrefix}input i ON i.cnt = tr.cnt
    LEFT JOIN ${schemaPrefix}output o ON o.cnt = tr.cnt
    -- Removed: LEFT JOIN error e ON e.cnt = tr.cnt
    WHERE tr.tsn_id = ?
    ORDER BY tr.seq_index
  `;

  try {
    // First, get the session - don't use destructuring as DirectSshConnection returns rows directly
    // Handle both MySQL2 format [rows, fields] and DirectSshConnection format (rows)
    const sessionResult = await connection.query(sessionSql, [tsn_id]);
    const sessionRows = Array.isArray(sessionResult) && sessionResult.length > 0 && !sessionResult[0].tsn_id ? 
      sessionResult[0] : // MySQL2 format
      sessionResult;     // DirectSshConnection format
    if (sessionRows.length === 0) {
      // console.log(`Test session not found for tsn_id: ${tsn_id}`);
      return null;
    }
    const session = sessionRows[0];

    // Log before querying test cases
    console.log(`[DEBUG] getTestSessionDetails: About to query test_result for tsn_id: ${tsn_id}`);
    let testCaseRows; // Removed fields_tc as it's not used
    try {
      // Get the test cases - handle both return formats
      const testCaseResult = await connection.query(testCasesSql, [tsn_id]);
      // FIXED: Removed 'const' to avoid variable shadowing - use the outer variable
      testCaseRows = Array.isArray(testCaseResult) && testCaseResult.length > 0 && !testCaseResult[0].tc_id ? 
        testCaseResult[0] : // MySQL2 format
        testCaseResult;     // DirectSshConnection format
      // Log after querying test cases
      console.log(`[DEBUG] getTestSessionDetails: test_result query for tsn_id: ${tsn_id} returned ${testCaseRows ? testCaseRows.length : 'undefined/null'} rows.`);
      if (testCaseRows && testCaseRows.length > 0) {
          console.log(`[DEBUG] getTestSessionDetails: First row from test_result:`, JSON.stringify(testCaseRows[0]));
      } else if (testCaseRows) {
          console.log(`[DEBUG] getTestSessionDetails: test_result query returned an empty array.`);
      } else {
          console.log(`[DEBUG] getTestSessionDetails: test_result query returned null or undefined for rows.`);
      }
    } catch (dbError) {
      console.error(`[ERROR] getTestSessionDetails: Database error querying test_result for tsn_id ${tsn_id}:`, dbError);
      testCaseRows = []; // Ensure testCaseRows is an empty array on error
    }

    let dbTestCases = []; // Initialize dbTestCases

    if (testCaseRows && testCaseRows.length > 0) {
      // Enrich failed test cases with detailed failure context
      for (const testCaseRow of testCaseRows) {
        testCaseRow.failure_context = null; // Initialize
        if (testCaseRow.outcome === 'F' && testCaseRow.cnt) {
          const failureContextSql = `
            SELECT
                i.txt AS input_data,
                o.txt AS output_data,
                tr_context.tc_id,
                tr_context.seq_index,
                tr_context.outcome,
                tr_context.cnt
            FROM
                ${schemaPrefix}test_result tr_context
            LEFT JOIN ${schemaPrefix}input i ON i.cnt = tr_context.cnt /* Changed from JOIN to LEFT JOIN */
            LEFT JOIN ${schemaPrefix}output o ON o.cnt = tr_context.cnt /* Changed from JOIN to LEFT JOIN */
            WHERE
                tr_context.tsn_id = ?
                AND tr_context.cnt <= ?
            ORDER BY
                tr_context.cnt DESC
            LIMIT 5;`; // Fetch the failed step and 4 preceding ones
          // Now get failure context - handle both return formats
          const failureContextResult = await connection.query(failureContextSql, [tsn_id, testCaseRow.cnt]);
          const failureContextRows = Array.isArray(failureContextResult) && failureContextResult.length > 0 && !failureContextResult[0].tc_id ? 
            failureContextResult[0] : // MySQL2 format
            failureContextResult;     // DirectSshConnection format
          if (failureContextRows && failureContextRows.length > 0) {
            testCaseRow.failure_context = failureContextRows.reverse(); // Reverse to show in chronological order
            // Continue without context if this specific query fails
          }
        }
      }
      // Format database test cases
      dbTestCases = testCaseRows.map(row => ({
        tc_id: row.tc_id ? String(row.tc_id) : null,
        test_case_name: row.test_case_name || row.description || (row.tc_id ? `Test Case ${row.tc_id}` : 'Unknown Test Case'),
        description: row.description || '',
        outcome: row.outcome,
        status: row.outcome === 'P' ? 'Passed' : (row.outcome === 'F' ? 'Failed' : (row.outcome === 'S' ? 'Skipped' : row.outcome || 'Unknown')), // P -> Passed, F -> Failed
        creation_time: row.creation_time,
        cnt: row.cnt,
        seq_index: row.seq_index,
        input_data: row.input_data || '',  // Include input data
        output_data: row.output_data || '', // Include output data
        error_message: row.error_message || '', // Include error message
        failure_context: row.failure_context, // Added property
        source: 'database'
      }));
    } else if (session && session.error && String(session.error).toLowerCase().includes('fail')) { // Check if session is marked as failed
        console.log(`[DEBUG] getTestSessionDetails: test_result was empty for tsn_id: ${tsn_id}, but session status indicates Failure. Attempting to fetch first failure details.`);
        const firstFailureSql = `
            SELECT
                tr.tc_id, tr.seq_index, tr.outcome, tr.creation_time, tr.cnt,
                tc.name as test_case_name, tc.comments as description,
                (SELECT i.txt FROM ${schemaPrefix}input i WHERE i.cnt = tr.cnt LIMIT 1) as input_data,
                (SELECT o.txt FROM ${schemaPrefix}output o WHERE o.cnt = tr.cnt LIMIT 1) as output_data
            FROM ${schemaPrefix}test_result tr
            LEFT JOIN ${schemaPrefix}test_case tc ON tr.tc_id = tc.tc_id
            WHERE tr.tsn_id = ? AND tr.outcome = 'F'
            ORDER BY tr.creation_time ASC /* Or tr.seq_index ASC */
            LIMIT 1;
        `;
        try {
            const firstFailureRows = await connection.query(firstFailureSql, [tsn_id]);
            if (firstFailureRows && firstFailureRows.length > 0) {
                const row = firstFailureRows[0];
                console.log(`[DEBUG] getTestSessionDetails: Found first failure details via fallback:`, JSON.stringify(row));
                dbTestCases.push({
                    tc_id: row.tc_id ? String(row.tc_id) : null,
                    test_case_name: row.test_case_name || row.description || (row.tc_id ? `Test Case ${row.tc_id}` : 'Unknown Test Case'),
                    description: row.description || '',
                    outcome: row.outcome,
                    status: row.outcome === 'P' ? 'Passed' : (row.outcome === 'F' ? 'Failed' : row.outcome),
                    creation_time: row.creation_time,
                    cnt: row.cnt,
                    seq_index: row.seq_index,
                    input_data: row.input_data,  // Add this
                    output_data: row.output_data, // Add this
                    failure_context: null, // Or could try to build a minimal context here if needed
                    source: 'database_fallback_failure'
                });
            } else {
                console.log(`[DEBUG] getTestSessionDetails: No specific failed step found via fallback query for tsn_id: ${tsn_id}`);
            }
        } catch (fallbackError) {
            console.error(`[ERROR] getTestSessionDetails: Error fetching first failure details for tsn_id ${tsn_id}:`, fallbackError);
        }
    }

    // Extract test name from session data or report HTML
    let testName = session && session.test_name || ''; // Use test_name from sessionSql first
    if (!testName && session && session.report) { // Fallback to report parsing if not in sessionSql result
      const suiteMatch = session.report.match(/Suite: .+?>(\d+)<\/a>.+?&nbsp;(.+?)</);
      const caseMatch = session.report.match(/Case: .+?>(\d+)<\/a>.+?&nbsp;(.+?)</);

      if (suiteMatch && suiteMatch.length > 2) {
        testName = suiteMatch[2];
      } else if (caseMatch && caseMatch.length > 2) {
        testName = caseMatch[2];
      }
    }

    // If no test name was found, use a default based on the ID
    if (!testName && session) {
      if (session.tc_id) {
        testName = `TC-${session.tc_id}`;
      } else if (session.ts_id) {
        testName = `TS-${session.ts_id}`;
      } else if (session.pj_id) {
        testName = `PJ-${session.pj_id}`;
      } else if (session.tsn_id) {
        testName = `Session ${session.tsn_id}`;
      } else {
        testName = 'Unknown Session';
      }
    } else if (!testName) {
      testName = 'Unknown Test';
    }

    // Determine test type from session data
    const type = session && session.type || (session && session.tc_id
      ? 'Test Case'
      : (session && session.ts_id ? 'Test Suite' : 'Project'));

    // Calculate pass rate
    const totalCases = dbTestCases.length;
    const passedCases = dbTestCases.filter(tc => tc.status === 'Passed').length;
    const failedCases = dbTestCases.filter(tc => tc.status === 'Failed').length;
    const passRate = totalCases > 0
      ? Math.round((passedCases / totalCases) * 100)
      : 0;

    // Calculate duration if start_ts and end_ts are available
    let duration = null;
    if (session && session.start_ts && session.end_ts) {
      try {
        const start = new Date(session.start_ts);
        const end = new Date(session.end_ts);
        if (!isNaN(start.getTime()) && !isNaN(end.getTime())) {
          const durationMs = end - start;
          const minutes = Math.floor(durationMs / 60000);
          const seconds = Math.floor((durationMs % 60000) / 1000);
          duration = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }
      } catch (e) {
        console.error('Error calculating duration:', e);
      }
    }

    // Extract environment from report if available
    let environment = 'qa02'; // Default
    if (session && session.report) {
      environment = extractEnvironment(session.report); // Call the restored function
    }

    // Build the complete session details object with safe null handling
    return {
      tsn_id: session ? session.tsn_id : null,
      test_id: session ? (session.tc_id || session.ts_id || session.pj_id || '') : '',
      test_name: testName || 'Unknown Test',
      type: type || 'Unknown',
      envir: environment,
      status: session ? (session.error || (session.end_ts ? 'Completed' : 'Running')) : 'Unknown',
      start_time: session ? session.start_ts : null,
      end_time: session ? session.end_ts : null,
      duration: duration,
      total_cases: totalCases,
      passed_cases: passedCases,
      failed_cases: failedCases,
      skipped_cases: 0, // Not tracked in this database
      pass_rate: passRate,
      uid: session ? session.uid : null,
      // Format user display name from uid similar to createBasicSessionData
      user_display: (() => {
        let userDisplay = 'Unknown';
        if (session && session.uid) {
          const userId = session.uid;
          if (typeof userId === 'string' && userId.includes('@')) {
            userDisplay = userId.split('@')[0];
          } else if (userId) {
            userDisplay = String(userId);
          }
        }
        return userDisplay;
      })(),
      test_cases: dbTestCases,
      report_html: session ? session.report : null // Include the original HTML for rendering if needed
    };
  } catch (error) {
    console.error(`Error getting test session details for ID ${tsn_id}:`, error);
    throw error;
  }
}

// Restored extractEnvironment function
function extractEnvironment(report) {
  // Default fallback value
  let environment = 'qa02';

  if (!report) {
    return environment;
  }

  try {
    // First try the specific 'envir=' parameter which appears to be the canonical one
    const envirMatch = report.match(/[<,\s]envir=([^<\s,]+)/);
    if (envirMatch && envirMatch.length > 1) {
      return envirMatch[1];
    }

    // If that's not found, try the more generic 'environment=' parameter
    const environmentMatch = report.match(/[<,\s]environment=([^<\s,]+)/);
    if (environmentMatch && environmentMatch.length > 1) {
      return environmentMatch[1];
    }
  } catch (error) {
    console.error('❌ Error extracting environment information:', error);
  }

  return environment;
}

/**
 * Get test details by tsn_id
 * @param {Object} connection - Database connection
 * @param {Object} params - Parameters including tsn_id
 * @returns {Promise<Object>} - Test details
 */
async function getTestDetails(connection, params = {}) {
  const { tsn_id } = params;

  if (!tsn_id) {
    throw new Error('Missing required parameter: tsn_id');
  }

  console.log(`Getting test details for tsn_id: ${tsn_id}`);

  try {
    // Use the existing getTestSessionDetails function
    const sessionDetails = await getTestSessionDetails(connection, tsn_id);

    if (!sessionDetails) {
      console.log(`No test details found for tsn_id: ${tsn_id}`);
      return null;
    }

    // The session details already contain all the info we need
    return sessionDetails;
  } catch (error) {
    console.error('Error getting test details:', error);
    throw error;
  }
}

/**
 * Get detailed test steps including the last 10 steps before a failure
 * @param {Object} connection - Database connection
 * @param {string|number} tsn_id - Test session ID
 * @returns {Promise<Array>} - Array of test steps with context
 */
async function getTestStepsWithFailureContext(connection, tsn_id) {
  try {
    // First, find the first failure in the test session
    const firstFailureQuery = `
      SELECT MIN(tr1.seq_index) as first_failure_index
      FROM rgs_test.test_result tr1
      WHERE tr1.tsn_id = ? AND tr1.outcome = 'F'
    `;

    // Execute the query to find the first failure
    const firstFailureResult = await connection.query(firstFailureQuery, [tsn_id]);
    const firstFailureRows = Array.isArray(firstFailureResult) && firstFailureResult.length > 0 && !firstFailureResult[0].first_failure_index ? 
      firstFailureResult[0] : firstFailureResult;
    const firstFailureIndex = firstFailureRows && firstFailureRows[0] ? firstFailureRows[0].first_failure_index : null;

    if (!firstFailureIndex) {
      // If no failure found, return all steps (limited to 20)
      const allStepsQuery = `
        SELECT 
          tr.tc_id, 
          tr.seq_index, 
          tr.tsn_id,
          tr.outcome,
          tr.creation_time, 
          i.txt as input_data, 
          o.txt as output_data
        FROM rgs_test.test_result tr
        LEFT JOIN rgs_test.input i ON i.cnt = tr.cnt
        LEFT JOIN rgs_test.output o ON o.cnt = i.cnt
        WHERE tr.tsn_id = ?
        ORDER BY tr.seq_index ASC
        LIMIT 20
      `;
      const allStepsResult = await connection.query(allStepsQuery, [tsn_id]);
      return Array.isArray(allStepsResult) && allStepsResult.length > 0 && !allStepsResult[0].tc_id ? 
        allStepsResult[0] : allStepsResult;
    }

    // Calculate the start index (10 steps before the first failure, but not less than 1)
    const startIndex = Math.max(1, firstFailureIndex - 10);
    
    // Get the detailed steps including the failure context
    const stepsQuery = `
      SELECT 
        tr.tc_id, 
        tr.seq_index, 
        tr.tsn_id,
        tr.outcome,
        tr.creation_time, 
        i.txt as input_data, 
        o.txt as output_data
      FROM rgs_test.test_result tr
      LEFT JOIN rgs_test.input i ON i.cnt = tr.cnt
      LEFT JOIN rgs_test.output o ON o.cnt = i.cnt
      WHERE tr.tsn_id = ? 
        AND tr.seq_index BETWEEN ? AND ?
      ORDER BY tr.seq_index ASC
    `;

    // Execute the query with parameters
    const stepsResult = await connection.query(stepsQuery, [tsn_id, startIndex, firstFailureIndex + 10]);
    return Array.isArray(stepsResult) && stepsResult.length > 0 && !stepsResult[0].tc_id ? 
      stepsResult[0] : stepsResult;
  } catch (error) {
    console.error('Error in getTestStepsWithFailureContext:', error);
    throw error;
  }
}

module.exports = {
  getActiveTests,
  getRecentRuns,
  getTestSessionDetails,
  getTestDetails,
  extractEnvironment,
  getTestStepsWithFailureContext
};
