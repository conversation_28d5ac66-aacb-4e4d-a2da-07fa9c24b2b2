/**
 * Unified API Service for SmartTest Application
 *
 * Combines functionality from dashboard, reports, and config API services
 * while preserving all existing behavior patterns and response formats.
 * Enhanced with environment configuration support.
 */
class UnifiedApiService {
  constructor() {
    // Initialize environment configuration if available
    this.envConfig = this.loadEnvironmentConfig();

    // Initialize configuration based on module context
    this.moduleContext = this.detectModuleContext();
    this.initializeConfiguration();

    // Credentials management
    this.credentials = { uid: '', password: '' };
    this.loadCredentials();

    console.log(`Unified API Service initialized for ${this.moduleContext} module with environment: ${this.getCurrentEnvironment()}`);
  }

  /**
   * Load environment configuration if available
   */
  loadEnvironmentConfig() {
    try {
      // Try to load environment config if available
      if (typeof window !== 'undefined' && window.envConfig) {
        return window.envConfig;
      }
      return null;
    } catch (error) {
      console.warn('Environment configuration not available, using defaults:', error);
      return null;
    }
  }

  /**
   * Get current environment name
   */
  getCurrentEnvironment() {
    if (this.envConfig) {
      return this.envConfig.current.name;
    }
    return 'Default (QA02)';
  }

  /**
   * Detect which module is using this service based on current URL
   */
  detectModuleContext() {
    if (typeof window !== 'undefined') {
      const path = window.location.pathname;
      if (path.includes('/dashboard')) return 'dashboard';
      if (path.includes('/reports')) return 'reports';
      if (path.includes('/config')) return 'config';
    }
    return 'dashboard'; // default
  }

  /**
   * Initialize configuration based on module context and environment
   */
  initializeConfiguration() {
    // Dynamically determine the base URL from the current window location
    const origin = typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000';

    // Base URL configuration per module with environment support
    if (this.envConfig) {
      // For config module, always use local origin to avoid CORS issues
      // The local server will proxy requests to the external API
      this.baseUrls = {
        dashboard: this.envConfig.current.apiBaseUrl,
        reports: this.envConfig.current.apiBaseUrl,
        config: origin  // Always use local origin for config module
      };

      console.log(`Using environment-based URLs: dashboard/reports=${this.envConfig.current.apiBaseUrl}, config=${origin}`);
    } else {
      // Use the current origin for all modules
      this.baseUrls = {
        dashboard: origin,           // Use current origin
        reports: origin,            // Use current origin
        config: origin              // Use current origin
      };

      console.log(`Using dynamic base URL: ${origin}`);
    }

    // Content-Type configuration per module
    this.contentTypes = {
      dashboard: 'application/json',
      reports: 'application/json',
      config: 'application/x-www-form-urlencoded'
    };

    // Endpoints (unified from all three services)
    this.endpoints = {
      // Common endpoints
      caseRunner: '/api/case-runner',
      suiteRunner: '/api/suite-runner',
      testStatus: '/api/test-status',
      testReport: '/api/test-report',
      testReports: '/api/test-reports', // Fixed: added /api prefix to match server route
      testSuites: '/local/test-suites',
      testCases: '/local/test-cases',
      stopTest: '/api/stop-test',
      rerunFailed: '/api/rerun-failed',
      activeTests: '/local/active-tests',
      recentRuns: '/local/recent-runs',

      // Reports-specific endpoints
      testDetailsEndpoint: '/local/test-details',

      // Config-specific endpoints (uses different endpoint for run-suite)
      runSuite: '/run-suite'
    };

    // Default test parameters with environment support
    const envName = this.envConfig ? this.envConfig.currentEnv : 'qa02';
    this.defaultTestParams = {
      environment: envName,
      shell_host: 'jps-qa10-app01',
      file_path: '/home/<USER>/',
      operatorConfigs: 'operatorNameConfigs',
      kafka_server: 'kafka-qa-a0.lab.wagerworks.com',
      dataCenter: 'GU',
      rgs_env: envName,
      old_version: '0',
      networkType1: 'multi-site',
      networkType2: 'multi-site',
      sign: '-',
      rate_src: 'local'
    };
  }

  /**
   * Get base URL for current module context
   */
  getBaseUrl() {
    return this.baseUrls[this.moduleContext];
  }

  /**
   * Get content type for current module context
   */
  getContentType() {
    return this.contentTypes[this.moduleContext];
  }

  /**
   * Set API credentials (unified from all services)
   */
  setCredentials(username, password) {
    this.credentials = { uid: username, password: password };

    // Always set credentials in memory first (this always works)
    let stored = false;

    // Try to save to session storage
    try {
      sessionStorage.setItem('smarttest_uid', username);
      sessionStorage.setItem('smarttest_pwd', password);
      stored = true;
    } catch (error) {
      console.log('Cannot save credentials to sessionStorage due to security restrictions');
      
      // Try to store credentials in parent window if in iframe
      if (window.parent && window.parent !== window) {
        try {
          // Try to access parent's sessionStorage (may still fail due to security)
          window.parent.sessionStorage?.setItem('smarttest_uid', username);
          window.parent.sessionStorage?.setItem('smarttest_pwd', password);
          stored = true;
        } catch (parentError) {
          console.log('Cannot access parent window sessionStorage');
        }
      }
    }

    console.log(`API credentials set for user: ${username}, Stored in sessionStorage: ${stored}`);
    return true;
  }

  /**
   * Load credentials from session storage (unified from all services)
   */
  loadCredentials() {
    try {
      // Try to access sessionStorage with security restriction handling
      let uid = '', password = '';
      
      try {
        uid = sessionStorage.getItem('smarttest_uid');
        password = sessionStorage.getItem('smarttest_pwd');
      } catch (securityError) {
        // Handle security restrictions (iframe, cross-origin context)
        console.log('Cannot access sessionStorage due to security restrictions, trying alternatives');
        
        // Try to get credentials from parent window if in iframe
        if (window.parent && window.parent !== window) {
          try {
            // Try to access parent's sessionStorage (may still fail due to security)
            const parentUid = window.parent.sessionStorage?.getItem('smarttest_uid');
            const parentPwd = window.parent.sessionStorage?.getItem('smarttest_pwd');
            if (parentUid && parentPwd) {
              uid = parentUid;
              password = parentPwd;
            }
          } catch (parentError) {
            console.log('Cannot access parent window sessionStorage');
          }
        }
      }

      // If credentials were found, use them
      if (uid && password) {
        this.credentials = { uid, password };
        console.log(`Credentials loaded for user: ${uid}`);
        return true;
      }

      console.log('No valid credentials found, user needs to log in');
      this.credentials = { uid: '', password: '' };
      return false;
    } catch (error) {
      console.log('Error loading credentials:', error);
      this.credentials = { uid: '', password: '' };
      return false;
    }
  }

  /**
   * Get authentication parameters
   */
  getAuthParams() {
    return {
      uid: this.credentials.uid,
      password: this.credentials.password
    };
  }

  /**
   * Make a GET request (preserving all existing behavior patterns)
   */
  async getRequest(endpoint, params = {}) {
    try {
      // Determine URL construction based on module context and endpoint type
      let url;
      const isLocalEndpoint = endpoint.startsWith('/local/');
      const baseUrl = this.getBaseUrl();
      
      // Always use the current origin for all endpoints
      // This ensures consistent URL construction across the application
      url = baseUrl + (endpoint.startsWith('/') ? endpoint : '/' + endpoint);
      
      console.log(`Constructing URL: Base=${baseUrl}, Endpoint=${endpoint}, Full URL=${url}`);
      
      // Ensure we're not doubling up on slashes
      url = url.replace(/([^:]\/)\/+/g, '$1');

      console.log(`Making GET request to: ${url}`);

      // Add authentication parameters
      const allParams = {
        ...params,
        ...this.getAuthParams()
      };

      // Build query string
      const queryString = Object.entries(allParams)
        .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
        .join('&');

      // Make the request
      const response = await fetch(`${url}?${queryString}`);

      // Handle non-200 responses
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`API error (${response.status}): ${errorText}`);
        throw new Error(`API request failed with status ${response.status}`);
      }

      // Parse the response
      return await response.json();
    } catch (error) {
      console.error(`Error making GET request to ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Make a POST request (preserving module-specific behavior)
   */
  async postRequest(endpoint, params = {}) {
    try {
      const url = this.getBaseUrl() + (endpoint.startsWith('/') ? endpoint : '/' + endpoint);
      console.log(`Making POST request to: ${url}`);

      // Add authentication parameters
      const requestData = {
        ...params,
        ...this.getAuthParams()
      };

      // Build request options based on module context
      const options = {
        method: 'POST',
        headers: {
          'Content-Type': this.getContentType()
        }
      };

      // Handle body encoding based on module context
      if (this.moduleContext === 'config') {
        // Config module: use URLSearchParams for form encoding
        const formData = new URLSearchParams();
        Object.entries(requestData).forEach(([key, value]) => {
          formData.append(key, value);
        });
        options.body = formData;
      } else {
        // Dashboard/Reports modules: use JSON encoding
        options.body = JSON.stringify(requestData);
      }

      // Log request parameters for debugging (mask password)
      const logParams = {...requestData};
      if (logParams.password) logParams.password = '***';
      console.log('Request parameters:', logParams);

      // Make the request
      const response = await fetch(url, options);

      // Handle non-200 responses
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`API error (${response.status}): ${errorText}`);

        // Try to parse error as JSON if possible (dashboard/reports behavior)
        if (this.moduleContext !== 'config') {
          try {
            const errorJson = JSON.parse(errorText);
            if (errorJson && errorJson.message) {
              throw new Error(errorJson.message);
            }
          } catch (parseError) {
            // If can't parse JSON, just use the text
          }
        }

        throw new Error(`API request failed with status ${response.status}`);
      }

      // Parse JSON response
      const data = await response.json();

      // Add success property for config module compatibility
      if (this.moduleContext === 'config') {
        return { success: true, ...data };
      }

      return data;
    } catch (error) {
      console.error(`Error making POST request to ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Run a specific test case by ID (unified from all services)
   */
  async runTestCase(tcId, params = {}) {
    try {
      console.log('[DEBUG] UnifiedApiService.runTestCase: Received tcId:', tcId, 'tcId type:', typeof tcId);
      console.log('[DEBUG] UnifiedApiService.runTestCase: Received params (payload from frontend):', JSON.stringify(params, null, 2));
      
      // Convert tcId to a string or number if it's an object
      if (typeof tcId === 'object') {
        console.error('[CRITICAL] tcId is an object in UnifiedApiService! Converting to string.');
        tcId = String(tcId);
      }
      // Build test parameters based on module context
      let testParams;

      if (this.moduleContext === 'dashboard') {
        // Dashboard module behavior
        testParams = {
          tc_id: tcId,
          user_id: this.credentials.uid,
          username: this.credentials.uid,
          ...this.defaultTestParams,
          ...params
        };
      } else if (this.moduleContext === 'config') {
        // Config module behavior (minimal required parameters)
        testParams = {
          tc_id: tcId,
          envir: params.envir || 'qa02',
          shell_host: params.shell_host || 'jps-qa10-app01',
          ...params
        };
      } else {
        // Reports module behavior
        testParams = {
          tc_id: tcId,
          ...this.defaultTestParams,
          ...params
        };
      }

      console.log(`Running test case ${tcId} with params:`, testParams);
      console.log('[DEBUG] UnifiedApiService.runTestCase: Final testParams before API call:', JSON.stringify(testParams, null, 2));

      const response = await this.postRequest(this.endpoints.caseRunner, testParams);

      // Handle response based on module context
      if (this.moduleContext === 'dashboard') {
        if (response.success) {
          console.log(`Test case ${tcId} running with session ID: ${response.tsn_id}`);
          return response;
        } else {
          throw new Error(response.message || `Failed to run test case ${tcId}`);
        }
      } else if (this.moduleContext === 'config') {
        console.log(`Test case ${tcId} run initiated:`, response);
        return response;
      } else {
        // Reports module
        if (response && response.tsn_id) {
          return response;
        } else {
          throw new Error('Failed to get test suite run ID');
        }
      }
    } catch (error) {
      console.error(`Error running test case ${tcId}:`, error);
      throw error;
    }
  }

  /**
   * Run a test suite (unified from all services)
   */
  async runTestSuite(tsId, params = {}) {
    try {
      if (!tsId) {
        throw new Error('Test suite ID is required');
      }

      // Build test parameters based on module context
      let testParams;
      let endpoint;

      if (this.moduleContext === 'config') {
        // Config module uses different endpoint and parameters
        testParams = {
          ts_id: tsId,
          user_id: this.credentials.uid,
          username: this.credentials.uid,
          ...this.defaultTestParams,
          ...params
        };
        endpoint = this.endpoints.runSuite; // '/run-suite'
      } else {
        // Dashboard and Reports modules use caseRunner endpoint
        testParams = {
          ts_id: tsId,
          user_id: this.credentials.uid,
          username: this.credentials.uid,
          ...this.defaultTestParams,
          ...params
        };
        endpoint = this.endpoints.suiteRunner; // '/suite-runner'
      }

      console.log(`Running test suite ${tsId} with params:`, testParams);

      const response = await this.postRequest(endpoint, testParams);

      if (response && response.tsn_id) {
        console.log(`Test suite ${tsId} running with session ID: ${response.tsn_id}`);
        return response.tsn_id;
      } else {
        throw new Error(response.message || `Failed to run test suite ${tsId}`);
      }
    } catch (error) {
      console.error(`Error running test suite ${tsId}:`, error);
      throw error;
    }
  }

  /**
   * Get test status (unified from all services)
   */
  async getTestStatus(tsnId) {
    try {
      return await this.getRequest(this.endpoints.testStatus, { tsn_id: tsnId });
    } catch (error) {
      console.error(`Error getting test status for ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Get report summary (unified from all services)
   */
  async getReportSummary(tsnId) {
    try {
      if (this.moduleContext === 'dashboard') {
        // Dashboard uses REST-style URL pattern
        return await this.getRequest(`${this.endpoints.testReports}/${tsnId}/summary`);
      } else {
        // Reports and Config use query parameter pattern
        return await this.getRequest(this.endpoints.testReport, { tsn_id: tsnId });
      }
    } catch (error) {
      console.error(`Error getting report summary for ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Get test report (unified from all services)
   */
  async getTestReport(tsnId) {
    try {
      if (this.moduleContext === 'dashboard') {
        // Dashboard uses REST-style URL pattern
        return await this.getRequest(`${this.endpoints.testReports}/${tsnId}`);
      } else {
        // Reports and Config use query parameter pattern
        return await this.getRequest(this.endpoints.testReport, { tsn_id: tsnId });
      }
    } catch (error) {
      console.error(`Error getting report for ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Get available test suites (unified with response format handling)
   */
  async getTestSuites() {
    try {
      const response = await this.getRequest(this.endpoints.testSuites);

      // Handle different response formats based on module context
      if (this.moduleContext === 'dashboard') {
        return response.success && Array.isArray(response.data) ? response.data : [];
      } else {
        // Reports and Config modules
        return response.testSuites || [];
      }
    } catch (error) {
      console.error('Error getting test suites:', error);
      throw error;
    }
  }

  /**
   * Get available test cases (unified with response format handling)
   */
  async getTestCases() {
    try {
      const response = await this.getRequest(this.endpoints.testCases);

      // Handle different response formats based on module context
      if (this.moduleContext === 'dashboard') {
        return response.success && Array.isArray(response.data) ? response.data : [];
      } else if (this.moduleContext === 'config') {
        return response.success && Array.isArray(response.data) ? response.data : [];
      } else {
        // Reports module
        return response.testCases || [];
      }
    } catch (error) {
      console.error('Error getting test cases:', error);
      throw error;
    }
  }

  /**
   * Search test cases with criteria (config module specific)
   */
  async searchTestCases(criteria = {}) {
    try {
      console.log('UnifiedApiService.searchTestCases called with criteria:', criteria);
      const response = await this.getRequest(this.endpoints.testCases, criteria);

      // Handle different response formats based on module context
      if (this.moduleContext === 'dashboard') {
        return response.success && Array.isArray(response.data) ? response.data : [];
      } else if (this.moduleContext === 'config') {
        return response.success && Array.isArray(response.data) ? response.data : [];
      } else {
        // Reports module
        return response.testCases || [];
      }
    } catch (error) {
      console.error('Error searching test cases:', error);
      throw error;
    }
  }

  /**
   * Stop a running test (unified from all services)
   */
  async stopTest(tsnId) {
    try {
      const response = await this.postRequest(this.endpoints.stopTest, { tsn_id: tsnId });
      return response.success === true;
    } catch (error) {
      console.error(`Error stopping test ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Rerun failed tests (unified from all services)
   */
  async rerunFailedTests(tsnId, params = {}) {
    try {
      const response = await this.postRequest(this.endpoints.rerunFailed, {
        tsn_id: tsnId,
        user_id: this.credentials.uid,
        username: this.credentials.uid,
        ...params
      });

      if (response && response.tsn_id) {
        return response.tsn_id;
      } else {
        throw new Error('Failed to get test suite run ID for rerun');
      }
    } catch (error) {
      console.error(`Error rerunning failed tests from ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Get active tests (unified with response format handling)
   */
  async getActiveTests() {
    try {
      const response = await this.getRequest(this.endpoints.activeTests);

      // Handle different response formats based on module context
      if (this.moduleContext === 'dashboard') {
        return response.success && Array.isArray(response.data) ? response.data : [];
      } else {
        // Reports and Config modules
        return response.activeTests || [];
      }
    } catch (error) {
      console.error('Error getting active tests:', error);
      throw error;
    }
  }

  /**
   * Get dashboard data (dashboard module specific)
   */
  async getDashboardData() {
    try {
      const activeTestsData = await this.getActiveTests();

      const recentRuns = activeTestsData.map(test => ({
        id: test.tsn_id,
        type: 'Test Case',
        environment: 'QA02',
        status: test.status || 'running',
        startTime: test.latest_activity || new Date().toISOString(),
        duration: 0
      }));

      const total = activeTestsData.length;
      const successful = activeTestsData.filter(test => test.outcome === 'P').length;
      const failed = activeTestsData.filter(test => test.outcome === 'F').length;
      const running = activeTestsData.filter(test => !test.outcome).length;

      return {
        summary: { total, successful, failed, running },
        recent: recentRuns,
        environment: 'QA02'
      };
    } catch (error) {
      console.error('Error getting dashboard data:', error);
      throw error;
    }
  }

  /**
   * Get test reports (unified with response format handling)
   */
  async getTestReports(params = {}) {
    try {
      const response = await this.getRequest(this.endpoints.testReports, params);

      // Handle different response formats based on module context
      if (this.moduleContext === 'dashboard') {
        return response.success && Array.isArray(response.data) ? response.data : [];
      } else if (this.moduleContext === 'config') {
        return response.reports || [];
      } else {
        // Reports module - return full response object
        return response;
      }
    } catch (error) {
      console.error('Error getting test reports:', error);
      if (this.moduleContext === 'reports') {
        // Reports module expects structured error response
        return { success: false, message: error.message || 'Network or API error' };
      }
      throw error;
    }
  }

  /**
   * Get recent runs (reports module specific)
   */
  async getRecentRuns(options = {}) {
    try {
      const response = await this.getRequest(this.endpoints.recentRuns, options);

      // Handle both response formats for backward compatibility
      if (response.success && Array.isArray(response.data)) {
        return response.data;
      } else if (Array.isArray(response)) {
        return response;
      }
      return [];
    } catch (error) {
      console.error('Error getting recent runs:', error);
      throw error;
    }
  }

  /**
   * Get test details (reports module specific)
   * @param {string} tsnId - Test session ID
   * @returns {Promise<Object>} - Test details with test cases
   */
  async getTestDetails(tsnId) {
    try {
      console.log(`Getting test details for ${tsnId} using endpoint ${this.endpoints.testDetailsEndpoint}`);

      const response = await this.getRequest(`${this.endpoints.testDetailsEndpoint}/${tsnId}`);
      console.log(`[DEBUG] getTestDetails: Raw API response:`, response);

      let result;
      // Handle different response formats - our standardized endpoint now returns both test and data properties
      if (response.success && response.test) {
        console.log(`[DEBUG] getTestDetails: Found test in response.test`);
        result = response.test;
      } else if (response.success && response.data) {
        console.log(`[DEBUG] getTestDetails: Found test in response.data`);
        result = response.data;
      } else if (response.test) {
        console.log(`[DEBUG] getTestDetails: Found direct test property`);
        result = response.test;
      } else {
        console.log(`[DEBUG] getTestDetails: Using full response as result`);
        result = response;
      }

      // Validate and log details about test cases in the result
      if (result) {
        console.log(`[DEBUG] getTestDetails: Test cases in result: ${result.test_cases ? result.test_cases.length : 'none'}`);
        if (result.test_cases && result.test_cases.length > 0) {
          console.log(`[DEBUG] getTestDetails: First test case:`, result.test_cases[0]);
        } else {
          console.warn(`[DEBUG] getTestDetails: No test cases found in the result for ${tsnId}`);
        }
      } else {
        console.error(`[DEBUG] getTestDetails: No valid result data found for ${tsnId}`);
      }

      return result;
    } catch (error) {
      console.error(`Error getting test details for ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Get default test parameters
   */
  static get DEFAULT_TEST_PARAMS() {
    return {
      environment: 'qa02',
      shell_host: 'jps-qa10-app01',
      file_path: '/home/<USER>/',
      operatorConfigs: 'operatorNameConfigs',
      kafka_server: 'kafka-qa-a0.lab.wagerworks.com',
      dataCenter: 'GU',
      rgs_env: 'qa02',
      old_version: '0',
      networkType1: 'multi-site',
      networkType2: 'multi-site',
      sign: '-',
      rate_src: 'local'
    };
  }
}

// CRITICAL: Immediately make the class and instance globally available
// This must happen synchronously for other scripts to find it
if (typeof window !== 'undefined') {
  // Make the class available globally
  window.UnifiedApiService = UnifiedApiService;
  
  // Create a global singleton instance - THIS IS THE PRIMARY API SERVICE INSTANCE
  window.apiService = new UnifiedApiService();
  
  // For backward compatibility
  window.unifiedApiService = window.apiService;
  
  console.log('UnifiedApiService class and global apiService instance initialized');
  
  // Dispatch a custom event to notify all scripts that API service is ready
  // This helps with timing issues and race conditions
  setTimeout(() => {
    document.dispatchEvent(new CustomEvent('apiservice-ready', {
      detail: { apiService: window.apiService }
    }));
    console.log('Dispatched apiservice-ready event');
  }, 100);
}

// Make UnifiedApiService class available globally for non-module scripts (Dashboard/Reports)
// This is essential since Dashboard loads this file as a regular script
window.UnifiedApiService = UnifiedApiService;

// Export for CommonJS (Node.js) environments if module is defined
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { 
    UnifiedApiService: UnifiedApiService,
    unifiedApiService: window.unifiedApiService 
  };
}

// DO NOT use ES6 export syntax here - it will break Dashboard/Reports
// ES6 export statements are syntax errors in regular script contexts

// For module compatibility, we use a technique that doesn't involve ES6 exports
// Custom Test Runner now uses window.UnifiedApiService directly

// Console output for debugging
console.log('Unified API Service loaded with global compatibility mode');


