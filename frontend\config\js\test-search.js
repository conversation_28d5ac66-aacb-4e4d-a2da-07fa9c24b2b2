/**
 * Test Case Search Functionality
 * Handles searching, filtering, and displaying test cases
 */

// Initialize when document is ready
console.log('Loading test-search.js script...');

// Helper function to reset search button state
function resetSearchButton() {
    const searchBtn = document.getElementById('search-test-cases-btn');
    if (searchBtn) {
        searchBtn.innerHTML = '<span class="ms-Button-label">Search</span>';
        searchBtn.disabled = false;
        console.log('Search button reset to default state');
    }
}

document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded in test-search.js');
    
    // Wait for API service to be available
    function checkApiAvailability() {
        console.log('Checking if API service is available...');
        if (window.apiService && typeof window.apiService.searchTestCases === 'function') {
            console.log('API service is available with searchTestCases method!');
            initializeSearchForm();
        } else {
            console.log('API service or searchTestCases not available yet, waiting...');
            setTimeout(checkApiAvailability, 100); // Check again in 100ms
        }
    }
    
    // Also listen for the apiservice-ready custom event
    document.addEventListener('apiservice-ready', function() {
        console.log('Received apiservice-ready event, initializing search form');
        initializeSearchForm();
    });
    
    // Start checking for API availability
    checkApiAvailability();
});

/**
 * Initialize the search form with event listeners
 */
function initializeSearchForm() {
    const searchForm = document.getElementById('test-search-form');

    console.log('Initializing search form...');

    if (!searchForm) {
        console.error('Search form not found!');
        return;
    }

    // Handle search form submission (keeping for form reset functionality)
    searchForm.addEventListener('submit', async (event) => {
        event.preventDefault();
        await searchTestCases();
    });

    // Handle form reset
    searchForm.addEventListener('reset', () => {
        // Hide search results when form is cleared
        document.getElementById('search-results-card').style.display = 'none';
    });
}

/**
 * Search test cases using the API
 * @returns {Promise<void>}
 */
async function searchTestCases() {
    console.log('Starting test case search...');

    // Get search button reference and set to loading state
    const searchBtn = document.getElementById('search-test-cases-btn');

    if (searchBtn) {
        searchBtn.innerHTML = '<span class="ms-Button-label">Searching...</span>';
        searchBtn.disabled = true;

        // Fallback timeout to reset button if something goes wrong
        setTimeout(() => {
            if (searchBtn.disabled && searchBtn.innerHTML.includes('Searching...')) {
                console.warn('Search button timeout - resetting button state');
                resetSearchButton();
            }
        }, 30000); // 30 second timeout
    }
    
    try {
        // Get search parameters from form inputs
        const nameFilter = document.getElementById('test-case-name-search').value.trim();
        const commentFilter = document.getElementById('test-case-comment-search').value.trim();
        const statusFilter = document.getElementById('test-case-status-filter') ? 
                            document.getElementById('test-case-status-filter').value : '';
        
        // Skip empty search
        if (!nameFilter && !commentFilter && !statusFilter) {
            alert('Please enter at least one search criteria');
            return;
        }
        
        // Show loading state in the results container
        const resultsContainer = document.getElementById('search-results-container');
        if (resultsContainer) {
            resultsContainer.innerHTML = '<div class="ms-Spinner"></div><div class="ms-Spinner-label">Searching...</div>';
        }
        
        // Show the results card during loading
        const resultsCard = document.getElementById('search-results-card');
        if (resultsCard) {
            resultsCard.style.display = 'block';
        }
        
        // Build search criteria object
        const criteria = {
            limit: 50 // Limit results to avoid performance issues
        };
        
        // Only add non-empty filters
        if (nameFilter) criteria.name = nameFilter;
        if (commentFilter) criteria.comments = commentFilter;
        if (statusFilter) criteria.status = statusFilter;
        
        console.log('Searching test cases with criteria:', criteria);
        
        // Check if the apiService is available in the global scope
        if (!window.apiService) {
            console.error('API Service is not available. Make sure services are properly initialized.');
            throw new Error('API Service is not available');
        }

        // Debug: Log what methods are available
        console.log('Available apiService methods:', Object.keys(window.apiService));
        console.log('apiService.searchTestCases type:', typeof window.apiService.searchTestCases);

        // Check if the searchTestCases method exists
        if (typeof window.apiService.searchTestCases !== 'function') {
            console.error('API Service does not have searchTestCases method. Using fallback.');
            throw new Error('Search functionality is not available');
        }
        
        // Call API service to search test cases
        const results = await window.apiService.searchTestCases(criteria);
        
        console.log('Search results:', results);
        
        if (!Array.isArray(results)) {
            console.error('Search results is not an array:', results);
            throw new Error('Invalid response format');
        }
        
        // Display results in UI
        displaySearchResults(results);
    } catch (error) {
        console.error('Error searching test cases:', error);
        showSearchError(`Error searching test cases: ${error.message || 'Unknown error'}`);
    } finally {
        // Reset button state using helper function
        resetSearchButton();
    }
}

/**
 * Display search results in the UI
 * @param {Array} results - Test case search results
 */
function displaySearchResults(results) {
    const resultsCard = document.getElementById('search-results-card');
    const resultsContainer = document.getElementById('search-results-container');
    const resultsCount = document.getElementById('search-results-count');
    
    // Show results card
    resultsCard.style.display = 'block';

    // Update count
    resultsCount.textContent = results.length;

    // Scroll to results card
    setTimeout(() => {
        resultsCard.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }, 100);
    
    // Clear previous results
    resultsContainer.innerHTML = '';
    
    if (results.length === 0) {
        resultsContainer.innerHTML = '<div class="ms-empty-message">No matching test cases found</div>';
        return;
    }
    
    // Create table
    const table = document.createElement('table');
    table.className = 'ms-Table teams-table';
    
    // Add headers
    const headers = ['TC ID', 'Name', 'Status', 'Owner', 'Comment', 'Actions'];
    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');
    
    headers.forEach(header => {
        const th = document.createElement('th');
        th.textContent = header;
        headerRow.appendChild(th);
    });
    
    thead.appendChild(headerRow);
    table.appendChild(thead);
    
    // Add results
    const tbody = document.createElement('tbody');
    
    results.forEach(testCase => {
        const row = document.createElement('tr');
        
        // Add cells for each field
        [
            testCase.tc_id,
            testCase.name || 'N/A',
            testCase.status || 'N/A',
            testCase.uid || 'N/A',
            testCase.comments || 'N/A'
        ].forEach((value, index) => {
            const td = document.createElement('td');
            
            // Limit comment length to prevent table layout issues
            if (index === 4 && value.length > 100) {
                td.textContent = value.substring(0, 100) + '...';
                td.title = value; // Show full comment on hover
            } else {
                td.textContent = value;
            }
            
            row.appendChild(td);
        });
        
        // Add action buttons
        const actionCell = document.createElement('td');
        
        // Select button
        const selectBtn = document.createElement('button');
        selectBtn.className = 'ms-Button ms-Button--primary ms-Button--small';
        selectBtn.innerHTML = '<span class="ms-Button-label">Select</span>';
        selectBtn.addEventListener('click', () => {
            // Set the test case ID in the run form
            document.getElementById('test-case-id').value = testCase.tc_id;

            // Notify FAB about test case selection
            document.dispatchEvent(new CustomEvent('test-case-selected', {
                detail: { testCaseId: testCase.tc_id, testCase: testCase }
            }));
            
            // Highlight the run test card to draw attention
            const runTestCard = document.getElementById('test-case-id')?.closest('.ms-card');

            if (runTestCard) {
                runTestCard.classList.add('highlighted');

                // After a short delay, remove the highlight
                setTimeout(() => {
                    runTestCard.classList.remove('highlighted');
                }, 2000);

                // Scroll to the run form
                runTestCard.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
            
            // Show a tooltip to guide the user
            if (typeof showSuccess === 'function') {
                showSuccess(`Test case ${testCase.tc_id} selected - you can now click 'Run Test'`);
            } else {
                // Create a temporary notification if the global showSuccess function is not available
                const notification = document.createElement('div');
                notification.className = 'ms-toast ms-toast-success';
                notification.innerHTML = `
                    <div class="ms-toast-icon">✓</div>
                    <div class="ms-toast-message">Test case ${testCase.tc_id} selected - you can now click 'Run Test'</div>
                `;
                document.body.appendChild(notification);
                setTimeout(() => {
                    notification.classList.add('show');
                }, 10);
                setTimeout(() => {
                    notification.classList.remove('show');
                    setTimeout(() => { document.body.removeChild(notification); }, 300);
                }, 3000);
            }
        });
        
        actionCell.appendChild(selectBtn);
        row.appendChild(actionCell);
        
        tbody.appendChild(row);
    });
    
    table.appendChild(tbody);
    resultsContainer.appendChild(table);
}

/**
 * Show search error message
 * @param {string} message - Error message to display
 */
function showSearchError(message) {
    const resultsCard = document.getElementById('search-results-card');
    const resultsContainer = document.getElementById('search-results-container');
    
    // Show results card
    resultsCard.style.display = 'block';
    
    // Show error message
    resultsContainer.innerHTML = `<div class="ms-MessageBar ms-MessageBar--error">
        <div class="ms-MessageBar-content">
            <div class="ms-MessageBar-text">
                ${message}
            </div>
        </div>
    </div>`;
}
