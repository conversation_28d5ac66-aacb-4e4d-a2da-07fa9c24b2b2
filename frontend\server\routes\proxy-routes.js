/**
 * Proxy Routes
 */
const express = require('express');
const router = express.Router();
const fetch = require('node-fetch');
const proxyToMptsr = require('../middleware/proxy');
const { getTestStatus } = require('../services/test-status');
const { stopTest } = require('../services/stop-test');
const { validateCredentials } = require('../middleware/auth');

// Test Status API endpoint - uses ReportSummary with cookie auth
router.get('/test-status', validateCredentials, async (req, res, next) => {
  try {
    console.log('GET /api/test-status');
    // Make a copy of query params to avoid consuming them multiple times
    const params = {...req.query};

    // Extract the test session ID and credentials
    const { tsn_id, uid, password } = params;

    try {
      // Get test status using the test-status service
      const status = await getTestStatus(tsn_id, uid, password);

      // Return the status
      return res.json({
        success: true,
        ...status
      });
    } catch (error) {
      console.error(`[/api/test-status] Error getting test status:`, error);
      return res.status(500).json({
        success: false,
        message: `Failed to get test status: ${error.message}`
      });
    }
  } catch (error) {
    console.error('Error in test-status endpoint:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// Stop Test API endpoint - uses RemoveSession with cookie auth
router.post('/stop-test', validateCredentials, async (req, res, next) => {
  try {
    console.log('POST /api/stop-test');
    // Make a copy of request body to avoid consuming it multiple times
    const params = {...req.body};

    // Extract the test session ID and credentials
    const { tsn_id, uid, password } = params;

    try {
      // Stop the test using the stop-test service
      const result = await stopTest(tsn_id, uid, password);

      // Return the result
      return res.json({
        success: true,
        ...result
      });
    } catch (error) {
      console.error(`[/api/stop-test] Error stopping test:`, error);
      return res.status(500).json({
        success: false,
        message: `Failed to stop test: ${error.message}`
      });
    }
  } catch (error) {
    console.error('Error in stop-test endpoint:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// Login Proxy API endpoint - uses Login with cookie auth
router.post('/login-proxy', async (req, res, next) => {
  try {
    console.log('POST /api/login-proxy');
    // Make a copy of request body to avoid consuming it multiple times
    const params = {...req.body};

    // Extract the credentials
    const { uid, password } = params;

    if (!uid || !password) {
      return res.status(400).json({
        success: false,
        message: 'Missing credentials (uid/password) required for authentication'
      });
    }

    try {
      // Get a valid JSESSIONID cookie using the cookie-auth service
      const { getJsessionId } = require('../services/cookie-auth');
      const jsessionId = await getJsessionId(uid, password);

      // Set the cookie in the response
      res.cookie('JSESSIONID', jsessionId, {
        path: '/',
        httpOnly: true,
        sameSite: 'lax',
        maxAge: 30 * 60 * 1000 // 30 minutes
      });

      // Return success
      return res.json({
        success: true,
        message: 'Login successful'
      });
    } catch (error) {
      console.error(`[/api/login-proxy] Error logging in:`, error);
      return res.status(401).json({
        success: false,
        message: `Login failed: ${error.message}`
      });
    }
  } catch (error) {
    console.error('Error in login-proxy endpoint:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// External API proxy routes for direct client access
// Helper function to check if external API is reachable
async function isExternalApiReachable(baseUrl) {
  try {
    // Simple HEAD request to check if the server is responding
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);
    
    const pingResponse = await fetch(baseUrl, {
      method: 'HEAD',
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    return true;
  } catch (error) {
    console.error(`External API not reachable at ${baseUrl}:`, error.message);
    return false;
  }
}

router.post('/external/login', async (req, res) => {
  try {
    console.log('POST /api/external/login');
    const { uid, password } = req.body;

    if (!uid || !password) {
      return res.status(400).json({
        success: false,
        message: 'Missing credentials (uid/password) required for authentication'
      });
    }
    
    // Define API base URL
    const baseUrl = 'http://mprts-qa02.lab.wagerworks.com:9080/AutoRun';
    
    // Check if external API is available first
    const isApiAvailable = await isExternalApiReachable(baseUrl);
    
    if (!isApiAvailable) {
      console.error('External API is not reachable. Using mock login response for development.');  
      // For development, provide a mock successful response
      res.cookie('JSESSIONID', 'mock-session-id', {
        path: '/',
        httpOnly: true,
        maxAge: 30 * 60 * 1000 // 30 minutes
      });
      
      return res.status(200).send(
        `<!DOCTYPE html><html><head><title>Login Success</title></head><body>
        <h1>Login Successful</h1>
        <p>Welcome ${uid}</p>
        </body></html>`
      );
    }

    // Forward to external API login endpoint
    const loginUrl = `${baseUrl}/Login`;
    
    const formData = new URLSearchParams();
    formData.append('uid', uid);
    formData.append('password', password);
    
    console.log(`[External Login] Forwarding to ${loginUrl}`);
    
    let response;
    try {
      // Add timeout to avoid hanging indefinitely
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout
      
      response = await fetch(loginUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Origin': baseUrl,
          'Referer': `${baseUrl}/Login`
        },
        body: formData,
        redirect: 'manual',
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
    } catch (fetchError) {
      if (fetchError.name === 'AbortError') {
        throw new Error('Connection to external API timed out after 10 seconds');
      }
      
      // Handle network errors specifically
      if (fetchError.code === 'ECONNREFUSED') {
        throw new Error(`Connection refused to external API at ${loginUrl}. Please check if the service is available.`);
      }
      
      if (fetchError.code === 'ENOTFOUND') {
        throw new Error(`External API host not found at ${loginUrl}. Please check network connectivity or DNS configuration.`);
      }
      
      // Rethrow other fetch errors with more details
      throw new Error(`Network error connecting to external API: ${fetchError.message}`);
    }
    
    // Forward Set-Cookie headers
    const setCookieHeader = response.headers.get('set-cookie');
    if (setCookieHeader) {
      // Get the Set-Cookie header value
      // The header might contain multiple cookies separated by comma
      const cookies = setCookieHeader.split(',');
      
      cookies.forEach(cookie => {
        // CRITICAL FIX: Change the Path from /AutoRun to / so the cookie works across all paths
        // Also remove Secure attribute and Domain for local development
        const finalCookie = cookie
          .replace(/; Secure/g, '')
          .replace(/; Domain=[^;]+/g, '')
          .replace(/; Path=\/AutoRun/g, '; Path=/');
          
        console.log(`[External Login] Original cookie: ${cookie}`);
        console.log(`[External Login] Modified cookie: ${finalCookie}`);
        
        res.append('Set-Cookie', finalCookie);
      });
      console.log(`[External Login] Forwarding cookies to client: ${cookies.length} cookies with fixed Path`);
    }
    
    const responseData = await response.text();
    
    // Extract JSESSIONID from cookie if it's been set
    // This could be used for troubleshooting if direct cookie setting doesn't work
    if (setCookieHeader) {
      const jsessionMatch = setCookieHeader.match(/JSESSIONID=([^;]+)/);
      if (jsessionMatch && jsessionMatch[1]) {
        const extractedJsessionId = jsessionMatch[1];
        console.log(`[External Login] Extracted JSESSIONID from Set-Cookie: ${extractedJsessionId}`);
        
        // Store in Express session for future API calls
        if (!req.session) {
          req.session = {};
        }
        req.session.jsessionId = extractedJsessionId;
        console.log(`[External Login] Stored JSESSIONID in session for future requests`);
        
        // Also set a client-side cookie with path / to ensure it's sent with all requests
        res.cookie('JSESSIONID', extractedJsessionId, {
          path: '/',
          maxAge: 30 * 60 * 1000, // 30 minutes
          httpOnly: false // Allow JavaScript access for debugging
        });
        console.log(`[External Login] Set additional client-side cookie with path /`);
      }
    }
    
    // Success response
    return res.status(response.status).send(responseData);
  } catch (error) {
    console.error('[External Login] Error:', error);
    return res.status(500).json({
      success: false,
      message: 'Error logging in to external API',
      error: error.message
    });
  }
});

// External API proxy endpoints for report data
router.get('/external/ReportSummary', async (req, res) => {
  try {
    console.log('GET /api/external/ReportSummary');
    const { tsn_id } = req.query;
    
    if (!tsn_id) {
      return res.status(400).json({
        success: false,
        message: 'Missing tsn_id parameter required for report summary'
      });
    }
    
    // Get cookies from request
    const cookies = req.headers.cookie || '';
    console.log(`[External ReportSummary] Incoming request cookies: ${cookies}`);
    
    // Extract JSESSIONID from the cookie header
    const jsessionId = cookies.match(/JSESSIONID=([^;]+)/)?.[1];
    console.log(`[External ReportSummary] Extracted JSESSIONID: ${jsessionId || 'NONE'}`);
    
    // Try to retrieve the cookie from the session if available
    let sessionCookie = req.session?.jsessionId;
    if (sessionCookie && !jsessionId) {
      console.log(`[External ReportSummary] Using JSESSIONID from session: ${sessionCookie}`);
    }
    
    // Use either the cookie from the request or from the session
    const effectiveJsessionId = jsessionId || sessionCookie;
    
    if (!effectiveJsessionId) {
      console.error(`[External ReportSummary] No JSESSIONID cookie found. Login required.`);
      return res.status(401).json({
        success: false,
        message: 'Missing JSESSIONID cookie. Please login first. This happens when cookies are not properly forwarded.'
      });
    }
    
    // Forward to external API endpoint
    const baseUrl = 'http://mprts-qa02.lab.wagerworks.com:9080/AutoRun';
    const summaryUrl = `${baseUrl}/ReportSummary?tsn_id=${encodeURIComponent(tsn_id)}`;
    
    console.log(`[External ReportSummary] Forwarding to ${summaryUrl}`);
    console.log(`[External ReportSummary] Using JSESSIONID: ${effectiveJsessionId}`);
    
    // Also store the session ID for future requests
    if (!req.session) {
      req.session = {};
    }
    req.session.jsessionId = effectiveJsessionId;
    
    let response;
    try {
      // Add timeout to avoid hanging indefinitely
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout
      
      response = await fetch(summaryUrl, {
        method: 'GET',
        headers: {
          'Cookie': `JSESSIONID=${effectiveJsessionId}`,
          'Referer': `${baseUrl}/ReportSummary`
        },
        redirect: 'follow',
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
    } catch (fetchError) {
      if (fetchError.name === 'AbortError') {
        throw new Error('Connection to external API timed out after 10 seconds');
      }
      
      // Handle network errors specifically
      if (fetchError.code === 'ECONNREFUSED' || fetchError.code === 'ENOTFOUND') {
        throw new Error(`Connection error to external API. Please check if the service is available.`);
      }
      
      // Rethrow other fetch errors with more details
      throw new Error(`Network error connecting to external API: ${fetchError.message}`);
    }
    
    if (!response.ok) {
      throw new Error(`ReportSummary request failed with status ${response.status}`);
    }
    
    const responseData = await response.text();
    return res.status(response.status).send(responseData);
  } catch (error) {
    console.error('[External ReportSummary] Error:', error);
    return res.status(500).json({
      success: false,
      message: 'Error fetching report summary',
      error: error.message
    });
  }
});

router.get('/external/ReportDetails', async (req, res) => {
  try {
    console.log('GET /api/external/ReportDetails');
    const { tsn_id, index } = req.query;
    
    if (!tsn_id) {
      return res.status(400).json({
        success: false,
        message: 'Missing tsn_id parameter required for report details'
      });
    }
    
    // Get cookies from request
    const cookies = req.headers.cookie || '';
    console.log(`[External ReportDetails] Incoming request cookies: ${cookies}`);
    
    // Extract JSESSIONID from the cookie header
    const jsessionId = cookies.match(/JSESSIONID=([^;]+)/)?.[1];
    console.log(`[External ReportDetails] Extracted JSESSIONID: ${jsessionId || 'NONE'}`);
    
    // Try to retrieve the cookie from the session if available
    let sessionCookie = req.session?.jsessionId;
    if (sessionCookie && !jsessionId) {
      console.log(`[External ReportDetails] Using JSESSIONID from session: ${sessionCookie}`);
    }
    
    // Use either the cookie from the request or from the session
    const effectiveJsessionId = jsessionId || sessionCookie;
    
    if (!effectiveJsessionId) {
      console.error(`[External ReportDetails] No JSESSIONID cookie found. Login required.`);
      return res.status(401).json({
        success: false,
        message: 'Missing JSESSIONID cookie. Please login first. This happens when cookies are not properly forwarded.'
      });
    }
    
    // Forward to external API endpoint
    const baseUrl = 'http://mprts-qa02.lab.wagerworks.com:9080/AutoRun';
    let detailsUrl = `${baseUrl}/ReportDetails?tsn_id=${encodeURIComponent(tsn_id)}`;
    
    // Add optional index parameter if provided
    if (index) {
      detailsUrl += `&index=${encodeURIComponent(index)}`;
    }
    
    console.log(`[External ReportDetails] Forwarding to ${detailsUrl}`);
    console.log(`[External ReportDetails] Using JSESSIONID: ${effectiveJsessionId}`);
    
    // Also store the session ID for future requests
    if (!req.session) {
      req.session = {};
    }
    req.session.jsessionId = effectiveJsessionId;
    
    let response;
    try {
      // Add timeout to avoid hanging indefinitely
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout
      
      response = await fetch(detailsUrl, {
        method: 'GET',
        headers: {
          'Cookie': `JSESSIONID=${effectiveJsessionId}`,
          'Referer': `${baseUrl}/ReportDetails`
        },
        redirect: 'follow',
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
    } catch (fetchError) {
      if (fetchError.name === 'AbortError') {
        throw new Error('Connection to external API timed out after 10 seconds');
      }
      
      // Handle network errors specifically
      if (fetchError.code === 'ECONNREFUSED' || fetchError.code === 'ENOTFOUND') {
        throw new Error(`Connection error to external API. Please check if the service is available.`);
      }
      
      // Rethrow other fetch errors with more details
      throw new Error(`Network error connecting to external API: ${fetchError.message}`);
    }
    
    if (!response.ok) {
      throw new Error(`ReportDetails request failed with status ${response.status}`);
    }
    
    const responseData = await response.text();
    return res.status(response.status).send(responseData);
  } catch (error) {
    console.error('[External ReportDetails] Error:', error);
    return res.status(500).json({
      success: false,
      message: 'Error fetching report details',
      error: error.message
    });
  }
});

// Apply the proxy middleware to all other routes
router.use((req, res, next) => {
  return proxyToMptsr(req, res, next);
});

module.exports = router;
