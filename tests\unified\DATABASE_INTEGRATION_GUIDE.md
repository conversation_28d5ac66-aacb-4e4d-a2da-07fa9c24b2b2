# Database Integration Testing Guide

**Date**: 2024-01-XX  
**Version**: 1.0  
**Environment**: SmartTest Unified Architecture

## Overview

This guide provides comprehensive documentation for database integration testing in the SmartTest unified architecture. The tests validate real database connectivity, query execution, API-database flow, and performance monitoring.

## Test Structure

### Database Integration Tests
**File**: `tests/unified/integration/database-integration.test.js`

**Purpose**: Tests direct database operations and connectivity
- Database connection management and health checks
- All database service methods with real queries
- Query parameter passing and result formatting
- Performance monitoring and logging
- Error scenarios and connection recovery

### API-Database Integration Tests
**File**: `tests/unified/integration/api-database-integration.test.js`

**Purpose**: Tests complete API-to-database flow
- API endpoint to database operation mapping
- Data transformation between database results and API responses
- Error handling when database operations fail
- Performance analysis of complete API-database flow
- Real data validation and response formatting

## Test Configuration

### Environment Setup
```javascript
const TEST_CONFIG = {
  environment: 'qa02',     // Database environment
  timeout: 30000,          // 30 second timeout
  maxRetries: 3,           // Maximum retry attempts
  logQueries: true         // Enable detailed query logging
};
```

### Database Connection
- **Direct SSH Connection**: Uses existing SSH configuration
- **No Tunneling**: Direct connection to database server
- **Environment**: QA02 database for testing
- **Health Checks**: Connection validation before test execution

## Running Database Tests

### All Database Tests
```bash
npm run test:database
```

### Specific Test Types
```bash
# Database integration tests only
npm run test:database:integration

# API-database integration tests only
npm run test:database:api

# Verbose logging
npm run test:database:verbose
```

### Custom Test Runner
```bash
# Run all database tests
node tests/unified/run-database-tests.js all

# Run specific test type
node tests/unified/run-database-tests.js database
node tests/unified/run-database-tests.js api-database

# Get help
node tests/unified/run-database-tests.js --help
```

## Test Features

### 1. Real Database Connectivity
- **Actual Database Connections**: Tests use real database connections
- **SSH Integration**: Leverages existing SSH configuration
- **Connection Health Checks**: Validates connectivity before test execution
- **Error Recovery**: Tests connection failure and recovery scenarios

### 2. Comprehensive Logging
```javascript
// Query execution logging
logger.query(sql, params, result);

// Performance monitoring
logger.performance(operation, duration);

// API response transformation
logger.transformation(input, output, duration);

// Error handling
logger.error(stage, error);
```

### 3. Performance Monitoring
```javascript
// Performance tracking for each operation
const timer = performanceTracker.startOperation('Database Query');
const result = await db.getTestCases(filters);
const duration = timer.end();

// Performance breakdown analysis
flowLogger.performance(operation, apiTime, dbTime, totalTime);
```

### 4. Real Data Testing
- **Actual Database Schema**: Tests use real database table structures
- **Real Query Results**: Validates actual data returned from database
- **Data Transformation**: Tests conversion from database format to API format
- **Response Validation**: Ensures API responses match expected structure

## Test Coverage

### Database Operations Tested
- **Test Cases**: `getTestCases()`, `getTestCaseById()`, `searchTestCases()`
- **Test Suites**: `getTestSuites()`, `getTestSuiteById()`, `getTestSuiteInfo()`
- **Test Sessions**: `getActiveTests()`, `getRecentRuns()`, `getTestSessionDetails()`
- **Test Results**: `getTestResults()`, `getTestResultSummary()`, `getTestCaseResults()`
- **Raw Queries**: `query()` with parameterized and raw SQL

### API Endpoints Tested
- **GET /local/test-cases**: Test cases retrieval with filters
- **GET /local/test-suites**: Test suites retrieval with pagination
- **GET /local/recent-runs**: Recent test runs with performance monitoring
- **Error Handling**: Database connection failures and recovery

### Performance Metrics
- **Database Query Time**: Time spent executing database queries
- **API Processing Time**: Time spent in API layer processing
- **Data Transformation Time**: Time spent converting data formats
- **Total Response Time**: Complete end-to-end response time
- **Performance Analysis**: Variance and optimization opportunities

## Logging Output Examples

### Database Query Logging
```
🔍 SQL Query: SELECT tc_id, tc_name FROM test_case WHERE tc_id > ? LIMIT ?
   Parameters: [0, 5]
   Result Count: 5 rows
   Sample Row: {
     "tc_id": 1001,
     "tc_name": "User Login Validation"
   }
```

### Performance Logging
```
⏱️  Performance - Get Test Cases: 245.67ms
📊 Performance Breakdown - GET /local/test-cases:
   API Processing: 45.23ms
   Database Query: 189.44ms
   Total Time: 245.67ms
   DB Percentage: 77.2%
```

### API Flow Logging
```
🌐 API Request: GET /local/test-cases
   Parameters: {"limit": 5}

🗄️  Database Query (189.44ms):
   SQL: SELECT * FROM test_case LIMIT ?
   Parameters: [5]

📊 Database Result: 5 rows
   Sample Row: {"tc_id": 1001, "tc_name": "User Login Validation"}

🔄 Data Transformation (12.34ms):
   Input Type: Array
   Output Type: Array
   Input Count: 5
   Output Count: 5

📤 API Response: 200 (245.67ms)
   Response Data: {"success": true, "data": [...], "count": 5}
```

## Error Handling

### Database Connection Errors
```javascript
test('should handle database connection errors gracefully', async () => {
  // Temporarily close database connection
  await db.close();
  
  const response = await request(testApp)
    .get('/local/test-cases')
    .expect(500);
  
  // Validate error response structure
  expect(response.body).toHaveProperty('success', false);
  expect(response.body).toHaveProperty('error');
});
```

### Query Execution Errors
```javascript
test('should handle invalid query gracefully', async () => {
  const invalidSql = 'SELECT * FROM non_existent_table';
  await expect(db.query(invalidSql)).rejects.toThrow();
});
```

## Performance Benchmarks

### Expected Performance Thresholds
- **Database Queries**: < 1000ms for standard queries
- **API Responses**: < 3000ms for complete API-database flow
- **Data Transformation**: < 100ms for standard datasets
- **Connection Establishment**: < 2000ms for initial connection

### Performance Analysis
```javascript
// Analyze performance patterns across multiple operations
const performanceTests = [
  { endpoint: '/local/test-cases', params: { limit: 5 } },
  { endpoint: '/local/test-suites', params: { limit: 5 } },
  { endpoint: '/local/recent-runs', params: { limit: 10 } }
];

// Performance assertions
expect(avgDuration).toBeLessThan(3000); // Average under 3 seconds
expect(maxDuration).toBeLessThan(5000); // Max under 5 seconds
```

## Troubleshooting

### Common Issues

#### 1. Database Connection Failures
**Symptoms**: Connection timeout or authentication errors
**Solutions**:
- Verify SSH connection configuration
- Check database server accessibility
- Validate credentials and permissions

#### 2. Query Execution Timeouts
**Symptoms**: Tests fail with timeout errors
**Solutions**:
- Increase test timeout configuration
- Optimize database queries
- Check database server performance

#### 3. Data Structure Mismatches
**Symptoms**: Test assertions fail on data structure validation
**Solutions**:
- Update mock data to match actual database schema
- Verify API response transformation logic
- Check database table structure changes

### Debug Mode
```bash
# Enable verbose logging
DB_LOG_LEVEL=verbose npm run test:database

# Run with debug output
DEBUG=* npm run test:database
```

## Best Practices

### 1. Test Data Management
- Use realistic test data that matches production patterns
- Implement proper test cleanup to avoid data pollution
- Maintain separate test database or use transactions for isolation

### 2. Performance Monitoring
- Always monitor query execution time
- Set realistic performance thresholds
- Analyze performance trends over time

### 3. Error Handling
- Test both success and failure scenarios
- Validate error response structures
- Ensure graceful degradation when database is unavailable

### 4. Logging and Debugging
- Use comprehensive logging for troubleshooting
- Include query parameters and results in logs
- Monitor performance metrics for optimization opportunities

## Integration with CI/CD

### Automated Testing
```yaml
# Example CI configuration
test-database:
  runs-on: ubuntu-latest
  steps:
    - name: Run Database Integration Tests
      run: npm run test:database
      env:
        DB_TEST_ENV: qa02
        DB_LOG_LEVEL: info
```

### Coverage Requirements
- **Database Operations**: 90%+ coverage for all database methods
- **API Endpoints**: 100% coverage for database-dependent endpoints
- **Error Scenarios**: 80%+ coverage for error handling paths

## Conclusion

The database integration tests provide comprehensive validation of the SmartTest unified architecture's database layer. They ensure:

1. **Real Database Connectivity** with proper error handling
2. **Complete API-Database Flow** validation with performance monitoring
3. **Data Integrity** through actual query execution and result validation
4. **Performance Optimization** through detailed timing analysis
5. **Error Recovery** through connection failure and recovery testing

This testing framework provides confidence in the database layer's reliability and performance, supporting the overall success of the unified architecture migration.
