/**
 * App configuration
 */

// App configuration
const PORT = process.env.PORT || 3000;
const BASE_URL = process.env.BASE_URL || 'http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/';

// Default test user credentials for testing
const TEST_USER = process.env.TEST_USER || '<EMAIL>';
const TEST_PASSWORD = process.env.TEST_PASSWORD || 'test';

// Alternative credentials for easy testing
const ALT_TEST_USERS = [
  { uid: '<EMAIL>', password: 'test' },
  { uid: '<EMAIL>', password: 'password' },
  { uid: 'Vita.<PERSON>@IGT.com', password: 'test' },
  { uid: 'Art<PERSON>.<PERSON><PERSON><PERSON>@IGT.com', password: 'test' },
  { uid: '<PERSON><PERSON>@IGT.com', password: 'test' },
  { uid: '<EMAIL>', password: 'test' }
];

// Default test parameters
const DEFAULT_PARAMS = {
  environment: process.env.DEFAULT_ENVIRONMENT || 'qa02',
  shell_host: process.env.DEFAULT_SHELL_HOST || 'jps-qa10-app01',
  file_path: process.env.DEFAULT_FILE_PATH || '/home/<USER>/',
  operatorConfigs: process.env.DEFAULT_OPERATOR_CONFIGS || 'operatorNameConfigs',
  kafka_server: process.env.DEFAULT_KAFKA_SERVER || 'kafka-qa-a0.lab.wagerworks.com',
  dataCenter: process.env.DEFAULT_DATA_CENTER || 'GU',
  rgs_env: process.env.DEFAULT_RGS_ENV || 'qa02',
  old_version: process.env.DEFAULT_OLD_VERSION || '0',
  networkType1: process.env.DEFAULT_NETWORK_TYPE1 || 'multi-site',
  networkType2: process.env.DEFAULT_NETWORK_TYPE2 || 'multi-site',
  sign: process.env.DEFAULT_SIGN || '-',
  rate_src: process.env.DEFAULT_RATE_SRC || 'local'
};

// Helper function to validate IDs
const isValidId = (id) => {
  return id && !isNaN(parseInt(id, 10));
};

module.exports = {
  PORT,
  BASE_URL,
  TEST_USER,
  TEST_PASSWORD,
  ALT_TEST_USERS,
  DEFAULT_PARAMS,
  isValidId
};
