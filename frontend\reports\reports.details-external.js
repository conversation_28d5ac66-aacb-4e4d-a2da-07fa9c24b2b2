async function loadTestDetailsFromExternalApi(testId, credentials) {
    try {
        console.log(`Loading test details from external API for ${testId}...`);

        // Use the external API service if available
        const externalApiService = window.externalApiService || window.enhancedExternalApiService;
        console.log('🔧 External API Service found:', !!externalApiService);
        console.log('🔧 Service type:', window.externalApiService ? 'externalApiService' : 'enhancedExternalApiService');

        if (!externalApiService) {
            throw new Error('External API Service is not available');
        }

        // Check session validity
        const isSessionValid = externalApiService.isSessionValid();
        console.log('🔐 Session valid:', isSessionValid);

        // Ensure we have valid credentials
        if (!isSessionValid) {
            console.log('🔑 Logging in to external API...');
            try {
                // Use the credentials already set in the service object
                if (!externalApiService.credentials || !externalApiService.credentials.uid || !externalApiService.credentials.password) {
                    console.warn('⚠️ No credentials in externalApiService - attempting to load from session storage');
                    // Try one more time to load from session storage
                    const sessionUid = sessionStorage.getItem('smarttest_uid');
                    const sessionPwd = sessionStorage.getItem('smarttest_pwd');
                    
                    if (sessionUid && sessionPwd) {
                        console.log(`🔐 Setting credentials from session storage: ${sessionUid}`);
                        externalApiService.credentials = { uid: sessionUid, password: sessionPwd };
                    } else {
                        console.error('⚠️ Unable to find credentials anywhere - login will likely fail');
                    }
                }
                
                const { uid, password } = externalApiService.credentials;
                console.log(`🔐 Attempting login with credentials: ${uid}`);
                await externalApiService.login(uid, password);
                console.log('✅ Login successful');
            } catch (loginError) {
                console.error('❌ Login failed:', loginError);
                throw new Error(`External API login failed: ${loginError.message}`);
            }
        }

        // Get report summary from external API - direct HTML response format
        console.log('📡 Fetching report summary...');

        // Use the same fetchOptions to ensure cookies are automatically sent
        // FIXED: Use proper cookie handling - browsers automatically include cookies with credentials: 'include'
        // Do NOT manually set Cookie header as browsers block this for security reasons
        const fetchOptions = {
            credentials: 'include', // This ensures cookies are automatically included
            headers: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
        };

        // Log session status for debugging
        if (externalApiService.jsessionId) {
            console.log(`🔑 External API session available: ${externalApiService.jsessionId === 'BROWSER_MANAGED' ? 'Browser-managed' : 'Explicit ID'}`);
        } else {
            console.warn('⚠️ No JSESSIONID found in ExternalApiService - this may cause 401 errors');
        }

        console.log('📡 Making request with credentials: include to ensure cookies are sent');
        const reportSummaryResponse = await fetch(`/api/external/ReportSummary?tsn_id=${testId}`, fetchOptions);

        console.log('📡 Summary response status:', reportSummaryResponse.status);
        console.log('📡 Summary response headers:', Object.fromEntries(reportSummaryResponse.headers.entries()));

        if (!reportSummaryResponse.ok) {
            if (reportSummaryResponse.status === 401) {
                console.error('❌ 401 Unauthorized - Authentication failed!');
                console.log('🔍 Debugging 401 error:');
                console.log('- Session ID:', externalApiService.jsessionId);
                console.log('- Session valid:', externalApiService.isSessionValid());
                console.log('- Document cookies:', document.cookie);
                console.log('- Request was made with credentials: include');

                // Try to re-login and retry once
                console.log('🔄 Attempting to re-login and retry...');
                try {
                    await externalApiService.login(credentials.uid, credentials.password);
                    console.log('✅ Re-login successful, retrying request...');

                    const retryResponse = await fetch(`/api/external/ReportSummary?tsn_id=${testId}`, fetchOptions);
                    if (retryResponse.ok) {
                        console.log('✅ Retry successful!');
                        // Continue with the retry response
                        const retryHtml = await retryResponse.text();
                        const retrySummaryData = parseReportSummaryHtml(retryHtml, testId);

                        // Continue with details request using retry pattern
                        const retryDetailsResponse = await fetch(`/api/external/ReportDetails?tsn_id=${testId}`, fetchOptions);
                        if (retryDetailsResponse.ok) {
                            const retryDetailsHtml = await retryDetailsResponse.text();
                            const retryDetailsData = parseReportDetailsHtml(retryDetailsHtml, testId);

                            // Build the final result
                            const testDetails = {
                                id: testId,
                                tsn_id: testId,
                                test_id: retrySummaryData.suite_id || '',
                                test_name: retrySummaryData.suite_name || 'Unknown Test',
                                name: retrySummaryData.suite_name || 'Unknown Test',
                                type: retrySummaryData.type || 'Test Suite',
                                environment: retrySummaryData.environment || 'Unknown',
                                status: retrySummaryData.status || 'Unknown',
                                start_time: retrySummaryData.start_time,
                                end_time: retrySummaryData.end_time,
                                duration: calculateDuration(retrySummaryData.start_time, retrySummaryData.end_time) || '0:00',
                                user: retrySummaryData.owner || credentials.uid || 'System',
                                trigger: retrySummaryData.trigger || 'Manual',
                                total_cases: retrySummaryData.total_cases || retryDetailsData.test_cases.length || 0,
                                passed_cases: retrySummaryData.passed_cases || 0,
                                failed_cases: retrySummaryData.failed_cases || 0,
                                skipped_cases: retrySummaryData.skipped_cases || 0,
                                test_cases: retryDetailsData.test_cases || [],
                                report: retryDetailsData.report_html || ''
                            };

                            currentState.currentTestDetails = testDetails;
                            console.log('✅ Test details loaded successfully after retry');
                            return testDetails;
                        }
                    }
                } catch (retryError) {
                    console.error('❌ Retry failed:', retryError);
                }

                // No fallback - external API must work
                console.error('🚫 EXTERNAL API ONLY MODE - No database fallback available');
            }

            throw new Error(`External API failed: ${reportSummaryResponse.status} ${reportSummaryResponse.statusText} - No database fallback in external API only mode`);
        }

        const summaryHtml = await reportSummaryResponse.text();
        console.log('📄 Summary HTML length:', summaryHtml.length);
        const summaryData = parseReportSummaryHtml(summaryHtml, testId);
        console.log('📊 Parsed summary data:', summaryData);

        // Get report details with all test cases - direct HTML response format
        console.log('📡 Fetching report details...');

        // Use the same fetchOptions to ensure cookies are automatically sent
        console.log('📡 Making details request with credentials: include to ensure cookies are sent');
        const reportDetailsResponse = await fetch(`/api/external/ReportDetails?tsn_id=${testId}`, fetchOptions);

        console.log('📡 Details response status:', reportDetailsResponse.status);
        if (!reportDetailsResponse.ok) {
            throw new Error(`Failed to get report details: ${reportDetailsResponse.status} ${reportDetailsResponse.statusText}`);
        }

        const detailsHtml = await reportDetailsResponse.text();
        console.log('📄 Details HTML length:', detailsHtml.length);
        const detailsData = parseReportDetailsHtml(detailsHtml, testId);
        console.log('📊 Parsed details data:', detailsData);

        // Combine summary and details into a unified format for our UI
        const testDetails = {
            id: testId,
            tsn_id: testId,
            test_id: summaryData.suite_id || '',
            test_name: summaryData.suite_name || 'Unknown Test',
            name: summaryData.suite_name || 'Unknown Test',
            type: summaryData.type || 'Test Suite',
            environment: summaryData.environment || 'Unknown',
            status: summaryData.status || 'Unknown',
            start_time: summaryData.start_time,
            end_time: summaryData.end_time,
            duration: calculateDuration(summaryData.start_time, summaryData.end_time) || '0:00',
            user: summaryData.owner || credentials.uid || 'System',
            trigger: summaryData.trigger || 'Manual',
            total_cases: summaryData.total_cases || (summaryData.passed_cases + summaryData.failed_cases) || 0,
            passed_cases: summaryData.passed_cases || 0,
            failed_cases: summaryData.failed_cases || 0,
            skipped_cases: summaryData.skipped_cases || 0,
            report: detailsData.report_html || '',
            originalParameters: summaryData.variables || {},
        };
        
        // Prioritize test_cases from detailsData as it contains the full 'steps' array.
        // Enrich with information from summaryData if available.
        if (detailsData.test_cases && detailsData.test_cases.length > 0) {
            console.log(`Processing ${detailsData.test_cases.length} test cases from detailsData (which includes steps).`);
            testDetails.test_cases = detailsData.test_cases.map(detailTc => {
                const summaryTc = (summaryData.test_cases || []).find(stc => stc.tc_id === detailTc.tc_id);
                if (summaryTc) {
                    // Merge: Start with detailTc (to keep its steps), then overwrite with summaryTc properties.
                    // 'status' from summaryData is generally preferred for the overall TC status.
                    // 'steps' from detailTc is crucial and must be preserved.
                    console.log(`Merging TC ID ${detailTc.tc_id}: Summary status='${summaryTc.status}', Detail steps count=${(detailTc.steps || []).length}`);
                    return {
                        ...detailTc, // Base object from details (includes 'steps', 'description', etc. from detail parse)
                        ...summaryTc, // Overwrite with summary data (e.g., 'status', 'name' if different or more accurate from summary)
                        steps: detailTc.steps || [], // CRITICAL: Ensure 'steps' from detailsData is always kept
                        status: summaryTc.status || detailTc.status, // Prefer summary status
                        hasFailures: (summaryTc.status ? summaryTc.status.toLowerCase() === 'failed' : (detailTc.status ? detailTc.status.toLowerCase() === 'failed' : false)),
                        // Ensure other relevant fields like description, name are consistently chosen (e.g., from summary if available)
                        description: summaryTc.description || detailTc.description || '',
                        name: summaryTc.name || detailTc.name || detailTc.tc_id // Fallback to tc_id if name is missing
                    };
                } else {
                    // No corresponding summary test case, use the detail one as is.
                    // Ensure 'steps' and 'hasFailures' are consistent.
                    console.log(`TC ID ${detailTc.tc_id} found in details but not in summary. Using details directly. Steps count: ${(detailTc.steps || []).length}`);
                    return {
                        ...detailTc,
                        steps: detailTc.steps || [],
                        hasFailures: detailTc.status ? detailTc.status.toLowerCase() === 'failed' : false,
                        name: detailTc.name || detailTc.tc_id // Fallback to tc_id if name is missing
                    };
                }
            });
            console.log(`Final merged test_cases count: ${testDetails.test_cases.length}`);
            testDetails.test_cases.forEach(tc => {
                if (!tc.steps) tc.steps = []; // Ensure steps array exists
                console.log(`  TC ID ${tc.tc_id}, Status: ${tc.status}, Steps: ${tc.steps.length}, HasFailures: ${tc.hasFailures}, Name: ${tc.name}`);
            });

        } else if (summaryData.test_cases && summaryData.test_cases.length > 0) {
            // Fallback: if detailsData had no test_cases for some reason, use summary (steps will be missing).
            console.warn('DetailsData had no test_cases. Falling back to summaryData.test_cases (detailed steps will be missing).');
            testDetails.test_cases = summaryData.test_cases.map(summaryTc => ({
                ...summaryTc,
                steps: [], // Explicitly state steps are missing
                hasFailures: summaryTc.status ? summaryTc.status.toLowerCase() === 'failed' : false,
                name: summaryTc.name || summaryTc.tc_id // Fallback to tc_id if name is missing
            }));
        } else {
            console.log('No test cases found in either summary or details data.');
            testDetails.test_cases = [];
        }

        // Update current state
        testDetails.originalParameters = summaryData.variables || detailsData.originalParameters ||  {};
        console.log('[DEBUG] loadTestDetailsFromExternalApi: Assembled testDetails.originalParameters:', JSON.stringify(testDetails.originalParameters, null, 2));
        
        console.log('Test details loaded from external API:', testDetails);

        // Return the test details so they can be used by the caller
        return testDetails;
    } catch (error) {
        // Log the specific error message and the full error object for more context
        const errorMessage = error.message || 'Unknown error loading test details from external API';
        console.error(`❌ EXTERNAL API ONLY MODE - Error in loadTestDetailsFromExternalApi for ${testId}: ${errorMessage}`, error);
        console.error('🚫 NO DATABASE FALLBACK - External API must be fixed to load test details');

        // Re-throw a new error with a clear message indicating no fallback
        throw new Error(`EXTERNAL API ONLY: Failed to load details for ${testId} from external API: ${errorMessage}. Database fallback is disabled.`);
    }
}

/**
 * Parse HTML from the Report Summary endpoint
 * @param {string} html - The HTML content to parse
 * @param {string} testId - The test ID
 * @returns {Object} The parsed summary data
 */
