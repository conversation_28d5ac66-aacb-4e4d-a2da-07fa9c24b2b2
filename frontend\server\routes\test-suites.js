/**
 * Test Suites Routes
 */
const express = require('express');
const router = express.Router();
const db = require('../database');
const { validateCredentials } = require('../middleware/auth');
const TestSuiteService = require('../services/test-suites');

// Get filtered test suites based on level and version
router.get('/test-suites/filtered', validateCredentials, async (req, res) => {
  try {
    const { level, version } = req.query;
    console.log('GET /api/test-suites/filtered', { level, version });
    
    // Get filtered test suites using the new service
    const filteredSuites = await TestSuiteService.getFilteredTestSuites(level, version);
    
    return res.json({
      success: true,
      data: filteredSuites || [],
      message: 'Filtered test suites retrieved successfully',
      filters: { level, version }
    });
  } catch (error) {
    console.error('Error retrieving filtered test suites:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve filtered test suites',
      error: error.message
    });
  }
});

// Get test suite by ID
router.get('/test-suites/:ts_id', validateCredentials, async (req, res) => {
  try {
    const { ts_id } = req.params;
    console.log('GET /api/test-suites/:ts_id', { ts_id });
    
    const testSuite = await TestSuiteService.getTestSuiteById(parseInt(ts_id));
    
    if (!testSuite) {
      return res.status(404).json({
        success: false,
        message: 'Test suite not found'
      });
    }
    
    return res.json({
      success: true,
      data: testSuite,
      message: 'Test suite retrieved successfully'
    });
  } catch (error) {
    console.error('Error retrieving test suite:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve test suite',
      error: error.message
    });
  }
});

// Search test suites by name
router.get('/test-suites/search/:namePattern', validateCredentials, async (req, res) => {
  try {
    const { namePattern } = req.params;
    console.log('GET /api/test-suites/search/:namePattern', { namePattern });
    
    const searchResults = await TestSuiteService.searchTestSuitesByName(namePattern);
    
    return res.json({
      success: true,
      data: searchResults || [],
      message: 'Test suite search completed successfully'
    });
  } catch (error) {
    console.error('Error searching test suites:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to search test suites',
      error: error.message
    });
  }
});

// Get filter options (levels and versions)
router.get('/test-suites/filter-options', validateCredentials, async (req, res) => {
  try {
    console.log('GET /api/test-suites/filter-options');
    
    const filterOptions = TestSuiteService.getFilterOptions();
    
    return res.json({
      success: true,
      data: filterOptions,
      message: 'Filter options retrieved successfully'
    });
  } catch (error) {
    console.error('Error retrieving filter options:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve filter options',
      error: error.message
    });
  }
});

// Get test suites (original endpoint enhanced)
router.get('/test-suites', validateCredentials, async (req, res) => {
  try {
    console.log('GET /api/test-suites');
    
    // Always use TestSuiteService.getFilteredTestSuites for consistent behavior
    // This handles both filtered and unfiltered cases (returns all when no filters)
    const { level, version } = req.query;
    const testSuites = await TestSuiteService.getFilteredTestSuites(level, version);
    
    return res.json({
      success: true,
      data: testSuites || [],
      message: 'Test suites retrieved successfully',
      filters: { level, version }
    });
  } catch (error) {
    console.error('Error retrieving test suites:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve test suites',
      error: error.message
    });
  }
});

module.exports = router;
