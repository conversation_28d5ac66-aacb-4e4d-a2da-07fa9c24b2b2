/* Test Details Modal Styles */

/* Modal overlay */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s ease-in-out;
}

.modal.show {
    display: block;
}

/* Modal content container */
.modal-content {
    background-color: #ffffff;
    margin: 5% auto;
    padding: 0;
    border: none;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    animation: slideIn 0.3s ease-out;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Modal header */
.modal-header {
    padding: 24px 32px 16px;
    border-bottom: 1px solid #e1dfdd;
    background-color: #f8f9fa;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #323130;
    font-size: 20px;
    font-weight: 600;
    line-height: 1.3;
}

/* Close button */
.close-btn {
    background: none;
    border: none;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #605e5c;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background-color: #e1dfdd;
    color: #323130;
}

.close-btn:focus {
    outline: 2px solid #0078d4;
    outline-offset: 2px;
}

/* Modal body */
.modal-body {
    padding: 24px 32px 32px;
}

/* Test details grid */
.test-details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 0;
}

/* Detail items */
.detail-item {
    background-color: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    border-left: 4px solid #0078d4;
    transition: all 0.2s ease;
}

.detail-item:hover {
    background-color: #f3f2f1;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.detail-item.full-width {
    grid-column: 1 / -1;
}

.detail-item strong {
    display: block;
    color: #323130;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
    opacity: 0.8;
}

.detail-item > *:not(strong) {
    color: #323130;
    font-size: 14px;
    line-height: 1.4;
    margin: 0;
}

/* Status badges */
.status-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-passed {
    background-color: #dff6dd;
    color: #107c10;
    border: 1px solid #c3e6cb;
}

.status-failed {
    background-color: #fde7e9;
    color: #d13438;
    border: 1px solid #f5c6cb;
}

.status-running {
    background-color: #fff4ce;
    color: #8a8886;
    border: 1px solid #ffeaa7;
}

.status-queued {
    background-color: #e1f5fe;
    color: #0078d4;
    border: 1px solid #bee5eb;
}

/* Buttons in modal */
.modal-body .ms-Button {
    margin-top: 8px;
}

.modal-body .ms-Button--primary {
    background-color: #0078d4;
    border-color: #0078d4;
    color: #ffffff;
    padding: 8px 16px;
    border-radius: 4px;
    text-decoration: none;
    display: inline-block;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.2s ease;
}

.modal-body .ms-Button--primary:hover {
    background-color: #106ebe;
    border-color: #106ebe;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 120, 212, 0.3);
}

/* Loading state */
.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e1dfdd;
    border-top: 3px solid #0078d4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

.loading-text {
    text-align: center;
    color: #605e5c;
    font-size: 14px;
    margin: 0;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .modal-content {
        margin: 10px;
        max-width: calc(100% - 20px);
        max-height: calc(100vh - 20px);
    }
    
    .test-details-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .modal-header {
        padding: 20px 24px 12px;
    }
    
    .modal-body {
        padding: 20px 24px 24px;
    }
    
    .detail-item {
        padding: 12px;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .detail-item {
        border: 2px solid #000;
    }
    
    .status-badge {
        border: 2px solid #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .modal,
    .modal-content,
    .detail-item,
    .close-btn,
    .ms-Button--primary {
        animation: none;
        transition: none;
    }
    
    .loading-spinner {
        animation: none;
    }
}
