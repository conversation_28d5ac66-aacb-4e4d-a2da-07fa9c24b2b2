/* Test Details Modal Styles */
.test-details-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.test-details-modal.show {
    display: flex;
}

.test-details-content {
    background: white;
    border-radius: 8px;
    max-width: 700px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    margin: 20px;
}

.test-details-header {
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.test-details-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
}

.close-btn:hover {
    background: #e0e0e0;
    color: #333;
}

.test-details-body {
    padding: 20px;
}

.test-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 25px;
}

.test-info-item {
    display: flex;
    flex-direction: column;
}

.test-info-label {
    font-weight: 600;
    color: #666;
    font-size: 12px;
    text-transform: uppercase;
    margin-bottom: 6px;
    letter-spacing: 0.5px;
}

.test-info-value {
    font-size: 14px;
    color: #333;
    word-break: break-word;
}

.status-badge {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    max-width: fit-content;
}

.status-success { background: #d4edda; color: #155724; }
.status-passed { background: #d4edda; color: #155724; }
.status-failed { background: #f8d7da; color: #721c24; }
.status-error { background: #f8d7da; color: #721c24; }
.status-running { background: #d1ecf1; color: #0c5460; }
.status-queued { background: #fff3cd; color: #856404; }
.status-unknown { background: #e2e3e5; color: #383d41; }

.parameters-section {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;
}

.parameters-section h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
}

.parameters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 12px;
}

.parameter-item {
    background: #f8f9fa;
    padding: 10px 12px;
    border-radius: 4px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 13px;
    border-left: 3px solid #0078d4;
}

.parameter-key {
    font-weight: 600;
    color: #0078d4;
}

.parameter-value {
    color: #333;
    margin-left: 8px;
}

.loading-spinner {
    text-align: center;
    padding: 40px;
}

.loading-spinner::before {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0078d4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-message {
    text-align: center;
    padding: 40px;
    color: #e81123;
}

/* Responsive design */
@media (max-width: 768px) {
    .test-details-content {
        margin: 10px;
        max-width: calc(100% - 20px);
    }
    
    .test-info-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .parameters-grid {
        grid-template-columns: 1fr;
    }
}