/**
 * Floating Action Button (FAB) for Quick Test Execution
 * Provides easy access to test running functionality from anywhere on the page
 */

class FloatingActionButton {
    constructor() {
        this.fabMain = document.getElementById('fab-main');
        this.quickTestPanel = document.getElementById('quick-test-panel');
        this.closeQuickTest = document.getElementById('close-quick-test');
        this.quickTestId = document.getElementById('quick-test-id');
        this.quickProjectDisplay = document.getElementById('quick-project-display');
        this.quickShellDisplay = document.getElementById('quick-shell-display');
        this.quickRunBtn = document.getElementById('quick-run-btn');

        // References to main form elements
        this.mainTestCaseInput = document.getElementById('test-case-id');
        this.mainProjectSelect = document.getElementById('project-select');
        this.mainShellSelect = document.getElementById('shell-host-select');
        
        this.isOpen = false;
        
        this.initializeEventListeners();
        this.setupKeyboardShortcuts();
        this.setupFormSynchronization();
    }

    initializeEventListeners() {
        // FAB main button click
        this.fabMain?.addEventListener('click', () => this.toggleQuickPanel());
        
        // Close button
        this.closeQuickTest?.addEventListener('click', () => this.closeQuickPanel());
        
        // Quick run button
        this.quickRunBtn?.addEventListener('click', () => this.runQuickTest());
        
        // Enter key in test ID input
        this.quickTestId?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.runQuickTest();
            }
        });
        
        // Auto-focus test ID input when panel opens
        this.quickTestPanel?.addEventListener('transitionend', () => {
            if (!this.quickTestPanel.classList.contains('hidden')) {
                this.quickTestId?.focus();
            }
        });
        
        // Click outside to close
        document.addEventListener('click', (e) => {
            if (this.isOpen && !this.quickTestPanel?.contains(e.target) && !this.fabMain?.contains(e.target)) {
                this.closeQuickPanel();
            }
        });
        
        // Escape key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.closeQuickPanel();
            }
        });
    }

    setupKeyboardShortcuts() {
        // Ctrl+Shift+R to open quick test panel
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'R') {
                e.preventDefault();
                this.toggleQuickPanel();
            }
        });
    }

    setupFormSynchronization() {
        // Listen for changes in main form to update FAB panel
        this.mainProjectSelect?.addEventListener('change', () => this.syncSettings());
        this.mainShellSelect?.addEventListener('change', () => this.syncSettings());
        this.mainTestCaseInput?.addEventListener('input', () => this.syncTestCaseId());

        // Listen for test case selection from search results
        document.addEventListener('test-case-selected', (e) => {
            if (e.detail && e.detail.testCaseId) {
                this.setTestId(e.detail.testCaseId);
            }
        });

        // Initial sync
        this.syncSettings();
    }

    syncSettings() {
        // Update FAB panel to show current main form settings
        const project = this.mainProjectSelect?.value || 'qa02';
        const shellHost = this.mainShellSelect?.value || 'jps-qa10-app01';

        if (this.quickProjectDisplay) {
            this.quickProjectDisplay.textContent = project.toUpperCase();
        }

        if (this.quickShellDisplay) {
            this.quickShellDisplay.textContent = shellHost;
        }
    }

    syncTestCaseId() {
        // Sync test case ID from main form to FAB panel
        const mainTestId = this.mainTestCaseInput?.value?.trim();
        if (mainTestId && this.quickTestId && !this.quickTestId.value) {
            this.quickTestId.value = mainTestId;
        }
    }

    toggleQuickPanel() {
        if (this.isOpen) {
            this.closeQuickPanel();
        } else {
            this.openQuickPanel();
        }
    }

    openQuickPanel() {
        this.quickTestPanel?.classList.remove('hidden');
        this.fabMain?.style.setProperty('transform', 'rotate(45deg)');
        this.isOpen = true;

        // Sync with main form first
        this.syncSettings();

        // Auto-populate test ID from main form or last used
        const mainTestId = this.mainTestCaseInput?.value?.trim();
        const lastTestId = localStorage.getItem('lastQuickTestId');

        if (this.quickTestId) {
            if (mainTestId) {
                // Use test ID from main form if available
                this.quickTestId.value = mainTestId;
            } else if (lastTestId) {
                // Fallback to last used test ID
                this.quickTestId.value = lastTestId;
            }
        }

        console.log('Quick test panel opened and synced');
    }

    closeQuickPanel() {
        this.quickTestPanel?.classList.add('hidden');
        this.fabMain?.style.setProperty('transform', 'rotate(0deg)');
        this.isOpen = false;
        
        console.log('Quick test panel closed');
    }

    async runQuickTest() {
        const testId = this.quickTestId?.value?.trim();
        const environment = this.mainProjectSelect?.value || 'qa02';
        const shellHost = this.mainShellSelect?.value || 'jps-qa10-app01';
        
        if (!testId) {
            this.showError('Please enter a test case ID');
            this.quickTestId?.focus();
            return;
        }
        
        // Validate test ID format (should be numeric)
        if (!/^\d+$/.test(testId)) {
            this.showError('Test ID should be numeric (e.g., 3386)');
            this.quickTestId?.focus();
            return;
        }
        
        try {
            // Save last used test ID
            localStorage.setItem('lastQuickTestId', testId);
            
            // Disable button during execution
            this.setRunButtonState(true, 'Running...');
            
            // Close the panel
            this.closeQuickPanel();
            
            // Show status bar with running state
            if (window.testStatusBar) {
                window.testStatusBar.updateTestStatus(testId, 'running');
            }
            
            // Also update main form with the test ID for consistency
            if (this.mainTestCaseInput) {
                this.mainTestCaseInput.value = testId;
            }

            // Call the existing test execution function
            await this.executeTest(testId, environment, shellHost);
            
        } catch (error) {
            console.error('Error running quick test:', error);
            this.showError(`Error: ${error.message || 'Unknown error'}`);
            
            // Re-open panel on error
            this.openQuickPanel();
        } finally {
            // Reset button state
            this.setRunButtonState(false, 'Run Test');
        }
    }

    async executeTest(testId, environment, shellHost) {
        // Use the existing test execution logic
        if (window.runTestCase && typeof window.runTestCase === 'function') {
            // Use existing function
            const result = await window.runTestCase(testId, environment, shellHost);
            
            if (result && result.tsn_id) {
                this.showSuccess(`Test ${testId} started successfully! TSN ID: ${result.tsn_id}`);
                
                // Trigger the existing addActiveTest function
                if (window.addActiveTest && typeof window.addActiveTest === 'function') {
                    window.addActiveTest(result);
                }
            } else {
                throw new Error(`Failed to start test ${testId}`);
            }
        } else {
            // Fallback: use API service directly
            if (window.apiService) {
                const result = await window.apiService.runTestCase(testId, {
                    envir: environment,
                    shell_host: shellHost
                });
                
                if (result && result.tsn_id) {
                    this.showSuccess(`Test ${testId} started successfully! TSN ID: ${result.tsn_id}`);
                } else {
                    throw new Error(`Failed to start test ${testId}`);
                }
            } else {
                throw new Error('API service not available');
            }
        }
    }

    setRunButtonState(disabled, text) {
        if (this.quickRunBtn) {
            this.quickRunBtn.disabled = disabled;
            const btnText = this.quickRunBtn.querySelector('.btn-text');
            const btnIcon = this.quickRunBtn.querySelector('.btn-icon');
            
            if (btnText) btnText.textContent = text;
            if (btnIcon) btnIcon.textContent = disabled ? '⏳' : '▶';
        }
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showNotification(message, type = 'info') {
        // Use existing notification system if available
        if (window.showNotification && typeof window.showNotification === 'function') {
            window.showNotification('Quick Test', message, type);
        } else if (window.testStatusBar) {
            window.testStatusBar.showMessage(message, type);
        } else {
            // Fallback to console and alert
            console.log(`${type.toUpperCase()}: ${message}`);
            if (type === 'error') {
                alert(`Error: ${message}`);
            }
        }
    }

    // Public methods for external control
    hide() {
        if (this.fabMain) {
            this.fabMain.style.display = 'none';
        }
    }

    show() {
        if (this.fabMain) {
            this.fabMain.style.display = 'flex';
        }
    }

    setTestId(testId) {
        if (this.quickTestId) {
            this.quickTestId.value = testId;
        }
    }
}

// Initialize FAB when DOM is ready
let floatingActionButton;

document.addEventListener('DOMContentLoaded', () => {
    floatingActionButton = new FloatingActionButton();
    console.log('Floating Action Button initialized');
    
    // Make it globally available
    window.floatingActionButton = floatingActionButton;
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FloatingActionButton;
}
